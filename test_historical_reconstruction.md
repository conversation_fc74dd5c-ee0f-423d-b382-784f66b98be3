# Historical Reconstruction Test Plan

## Testing the Historical SCD2 Reconstruction

### 1. API Endpoint Test
```bash
# Start historical reconstruction
curl -X POST "http://localhost:8080/jobs/reconstruction/historical/start"
```

### 2. Expected Behavior

**Before Historical Reconstruction:**
- Vehicles table has records with `effective_from = today`
- No historical timeline preserved

**After Historical Reconstruction:**
- Multiple vehicle records per vehicle spanning 2013-2024
- Each record has `effective_from` matching the EventData timeline
- Proper `effective_to` dates showing validity periods
- Complete SCD2 timeline reflecting actual historical changes

### 3. Database Verification Queries

```sql
-- Check vehicle timeline for a specific vehicle
SELECT 
    business_key,
    effective_from,
    effective_to,
    is_current,
    -- Add relevant vehicle fields
FROM vehicles 
WHERE business_key = 'your_vehicle_ref'
ORDER BY effective_from;

-- Check that effective dates match EventData timeline
SELECT DISTINCT 
    ed.effective_from as event_data_date,
    v.effective_from as vehicle_date
FROM event_data ed
JOIN events e ON ed.event_id = e.event_id
JOIN vehicles v ON e.vehicle_id = v.vehicle_id
WHERE v.business_key = 'your_vehicle_ref'
AND ed.effective_from = v.effective_from;
```

### 4. Key Improvements Made

1. **Historic Timestamp Preservation**: Vehicle `effective_from`/`effective_to` now reflect EventData timeline
2. **Chronological Processing**: Vehicles saved in chronological order to build proper SCD2 timeline
3. **Individual SCD2 Operations**: Using `saveNewVersion` instead of bulk operations to preserve timestamps
4. **Timeline Reconstruction**: Creates multiple vehicle versions for each time period from EventData

### 5. Performance Considerations

- Historical reconstruction processes all EventData for each vehicle
- Creates multiple SCD2 versions per vehicle (can be many for vehicles with long history)
- Uses individual saves instead of bulk operations for timestamp accuracy
- Should be run during maintenance windows for large datasets
