package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.utils.generators.toEventData
import org.junit.jupiter.api.Test
import kotlin.test.assertTrue

class TransportModeProcessorTest : BaseProcessorTest<TransportModeProcessor>() {
    override val processor = TransportModeProcessor()

    @Test
    fun `should return missing data error for any input`() {
        val eventData = toEventData("vehicleClass" to "Klasse II")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )

        assertTrue(result.isLeft(), "Expected error but got success")
        val error = (result as Either.Left).value
        assertTrue(error is ProcessingError.MissingRequiredData)
        assertTrue(error.message.contains("Transport mode data not available yet"))
    }

    @Test
    fun `should return missing data error for empty input`() {
        val result = processor.process(
            emptyMap(),
            emptyMap(),
            null,
            null
        )

        assertTrue(result.isLeft(), "Expected error but got success")
        val error = (result as Either.Left).value
        assertTrue(error is ProcessingError.MissingRequiredData)
        assertTrue(error.message.contains("Transport mode data not available yet"))
    }
}
