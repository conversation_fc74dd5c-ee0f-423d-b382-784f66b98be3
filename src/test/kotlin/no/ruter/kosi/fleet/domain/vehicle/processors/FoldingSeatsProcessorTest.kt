package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.utils.generators.toEventData
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class FoldingSeatsProcessorTest : BaseProcessorTest<FoldingSeatsProcessor>() {
    override val processor = FoldingSeatsProcessor()
    
    @Test
    fun `should process valid folding seats count`() {
        val eventData = toEventData("customAttr_Klappseter" to "2")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        assertEquals(2, processedField.value)
        assertEquals(QualityType.VALID, processedField.quality.type)
    }
    
    @Test
    fun `should process zero folding seats`() {
        val eventData = toEventData("customAttr_Klappseter" to "0")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        assertEquals(0, processedField.value)
        assertEquals(QualityType.VALID, processedField.quality.type)
    }
    
    @Test
    fun `should process large number of folding seats`() {
        val eventData = toEventData("customAttr_Klappseter" to "10")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )

        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        assertEquals(10, processedField.value)
        assertEquals(QualityType.VALID, processedField.quality.type)
    }
    
    @Test
    fun `should handle invalid folding seats count`() {
        val eventData = toEventData("customAttr_Klappseter" to "invalid")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isLeft(), "Expected error but got success")
        val error = (result as Either.Left).value
        assertTrue(error is ProcessingError.InvalidFormat)
        assertTrue(error.message.contains("Invalid folding seats count"))
    }

    @Test
    fun `should handle negative folding seats count`() {
        val eventData = toEventData("customAttr_Klappseter" to "-1")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )

        assertTrue(result.isLeft(), "Expected error but got success")
        val error = (result as Either.Left).value
        assertTrue(error is ProcessingError.InvalidFormat)
        assertTrue(error.message.contains("Invalid folding seats count"))
    }
    
    @Test
    fun `should handle missing folding seats data`() {
        val result = processor.process(
            emptyMap(),
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isLeft(), "Expected error but got success")
        val error = (result as Either.Left).value
        assertTrue(error is ProcessingError.MissingRequiredData)
        assertTrue(error.message.contains("No folding seats data found"))
    }
}
