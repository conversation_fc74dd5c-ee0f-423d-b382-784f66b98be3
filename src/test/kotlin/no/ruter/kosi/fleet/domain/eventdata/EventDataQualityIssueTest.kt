package no.ruter.kosi.fleet.domain.eventdata

import no.ruter.kosi.fleet.domain.quality.QualityType
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import java.time.Instant
import java.time.temporal.ChronoUnit

class EventDataQualityIssueTest {

    @Test
    fun `should create new version with updated fields`() {
        val now = Instant.now()
        val original = EventDataQualityIssue(
            eventDataId = 1,
            fieldName = "testField",
            actualValue = "oldValue",
            expectedValue = "expectedValue",
            type = QualityType.MISSING_VALUE,
            timestamp = now.minus(1, ChronoUnit.DAYS),
            businessKey = "1-testField"
        ).apply {
            effectiveFrom = now.minus(1, ChronoUnit.DAYS)
            isCurrent = true
        }

        val newVersion = EventDataQualityIssue.createNewVersion(
            previous = original,
            newActualValue = "updatedValue",
            newType = QualityType.INVALID_VALUE
        )

        assertAll(
            { assertEquals(1, newVersion.eventDataId) },
            { assertEquals("testField", newVersion.fieldName) },
            { assertEquals("updatedValue", newVersion.actualValue) },
            { assertEquals("expectedValue", newVersion.expectedValue) },
            { assertEquals(QualityType.INVALID_VALUE, newVersion.type) },
            { assertTrue(newVersion.timestamp.isAfter(original.timestamp)) },
            { assertEquals("1-testField", newVersion.businessKey) },
            { assertTrue(newVersion.isCurrent) },
            { assertTrue(newVersion.effectiveFrom.isAfter(original.effectiveFrom)) },
            { assertNull(newVersion.effectiveTo) }
        )
    }

    @Test
    fun `should maintain original values when creating new version with null parameters`() {
        val now = Instant.now()
        val original = EventDataQualityIssue(
            eventDataId = 1,
            fieldName = "testField",
            actualValue = "oldValue",
            expectedValue = "expectedValue",
            type = QualityType.MISSING_VALUE,
            timestamp = now.minus(1, ChronoUnit.DAYS),
            businessKey = "1-testField-MISSING"
        ).apply {
            effectiveFrom = now.minus(1, ChronoUnit.DAYS)
            isCurrent = true
        }

        val newVersion = EventDataQualityIssue.createNewVersion(
            previous = original,
            newActualValue = null,
            newExpectedValue = null,
            newType = null,
            businessKey = null
        )

        assertAll(
            { assertEquals("oldValue", newVersion.actualValue) },
            { assertEquals("expectedValue", newVersion.expectedValue) },
            { assertEquals(QualityType.MISSING_VALUE, newVersion.type) },
            { assertEquals("1-testField-MISSING", newVersion.businessKey) }
        )
    }

    @Test
    fun `should generate business key if not provided`() {
        val issue = EventDataQualityIssue(
            eventDataId = 1,
            fieldName = "testField",
            actualValue = "value",
            expectedValue = null,
            type = QualityType.INVALID_VALUE,
            timestamp = Instant.now(),
            businessKey = ""
        )

        assertEquals("1-testField", issue.businessKey)
    }

    @Test
    fun `should use provided business key`() {
        val issue = EventDataQualityIssue(
            eventDataId = 1,
            fieldName = "testField",
            actualValue = "value",
            expectedValue = null,
            type = QualityType.INVALID_VALUE,
            timestamp = Instant.now(),
            businessKey = "custom-key"
        )

        assertEquals("custom-key", issue.businessKey)
    }


    @Test
    fun `should have meaningful string representation`() {
        val now = Instant.now()
        val issue = EventDataQualityIssue(
            id = 1,
            eventDataId = 2,
            fieldName = "testField",
            actualValue = "value",
            expectedValue = null,
            type = QualityType.INVALID_VALUE,
            timestamp = now,
            businessKey = "1-testField-INVALID"
        ).apply {
            effectiveFrom = now
            isCurrent = true
        }

        val stringRepr = issue.toString()

        assertAll(
            { assertTrue(stringRepr.contains("id=1")) },
            { assertTrue(stringRepr.contains("eventDataId=2")) },
            { assertTrue(stringRepr.contains("fieldName='testField'")) },
            { assertTrue(stringRepr.contains("type=INVALID")) },
            { assertTrue(stringRepr.contains("isCurrent=true")) },
            { assertTrue(stringRepr.contains("businessKey='1-testField-INVALID'")) },
            { assertTrue(stringRepr.contains("effectiveFrom=$now")) }
        )
    }
}
