package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.utils.generators.toEventData
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class InternalNumberProcessorTest : BaseProcessorTest<InternalNumberProcessor>() {
    override val processor = InternalNumberProcessor()
    
    @Test
    fun `should process valid internal number`() {
        val eventData = toEventData("internalNumber" to "8912")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        assertEquals(8912, processedField.value)
        assertEquals(QualityType.VALID, processedField.quality.type)
    }
    
    @Test
    fun `should process internal number from custom attribute`() {
        val eventData = toEventData("customAttr_Internnummer_SIS" to "388912")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        assertEquals(388912, processedField.value)
        assertEquals(QualityType.VALID, processedField.quality.type)
    }
    
    @Test
    fun `should handle invalid internal number`() {
        val eventData = toEventData("internalNumber" to "invalid")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isLeft(), "Expected error but got success")
        val error = (result as Either.Left).value
        assertTrue(error is ProcessingError.InvalidFormat)
        assertTrue(error.message.contains("Invalid internal number"))
    }
    
    @Test
    fun `should handle negative internal number`() {
        val eventData = toEventData("internalNumber" to "-1")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isLeft(), "Expected error but got success")
        val error = (result as Either.Left).value
        assertTrue(error is ProcessingError.InvalidFormat)
    }
    
    @Test
    fun `should handle zero internal number`() {
        val eventData = toEventData("internalNumber" to "0")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isLeft(), "Expected error but got success")
        val error = (result as Either.Left).value
        assertTrue(error is ProcessingError.InvalidFormat)
    }
    
    @Test
    fun `should handle missing internal number`() {
        val result = processor.process(
            emptyMap(),
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isLeft(), "Expected error but got success")
        val error = (result as Either.Left).value
        assertTrue(error is ProcessingError.MissingRequiredData)
        assertTrue(error.message.contains("No internal number data found"))
    }
}
