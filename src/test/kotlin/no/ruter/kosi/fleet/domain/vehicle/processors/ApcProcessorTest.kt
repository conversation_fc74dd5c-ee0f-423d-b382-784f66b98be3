package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.vehicle.dto.APCInfoDto
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.utils.generators.toEventData
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class ApcProcessorTest : BaseProcessorTest<ApcProcessor>() {
    override val processor = ApcProcessor()
    
    @Test
    fun `should process valid APC count and type`() {
        val eventData = toEventData(
            "customAttr_APC_sensorer_antall" to "4",
            "customAttr_APC_sensorer_type" to "Iris Irma Matrix"
        )
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        val apcInfo = processedField.value as APCInfoDto
        assertEquals(4, apcInfo.count)
        assertEquals("Iris Irma Matrix", apcInfo.type)
        assertEquals(QualityType.VALID, processedField.quality.type)
        assertEquals("", processedField.quality.message)
    }
    
    @Test
    fun `should handle invalid count gracefully`() {
        val eventData = toEventData(
            "customAttr_APC_sensorer_antall" to "invalid",
            "customAttr_APC_sensorer_type" to "Iris Irma Matrix"
        )
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        val apcInfo = processedField.value as APCInfoDto
        assertEquals(0, apcInfo.count) // Should default to 0 for invalid count
        assertEquals("Iris Irma Matrix", apcInfo.type)
        assertEquals(QualityType.ENRICHED_VALUE, processedField.quality.type)
        assertEquals("Parsed APC count from 'invalid' to '0'", processedField.quality.message)
    }
    
    @Test
    fun `should handle missing count field`() {
        val eventData = toEventData(
            "customAttr_APC_sensorer_type" to "Iris Irma Matrix"
        )
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        val apcInfo = processedField.value as APCInfoDto
        assertEquals(0, apcInfo.count)
        assertEquals("Iris Irma Matrix", apcInfo.type)
        assertEquals(QualityType.MISSING_VALUE, processedField.quality.type)
        assertEquals("Partial APC data found", processedField.quality.message)
    }

    @Test
    fun `should handle missing type field`() {
        val eventData = toEventData(
            "customAttr_APC_sensorer_antall" to "2"
        )
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )

        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        val apcInfo = processedField.value as APCInfoDto
        assertEquals(2, apcInfo.count)
        assertEquals("", apcInfo.type)
        assertEquals(QualityType.MISSING_VALUE, processedField.quality.type)
        assertEquals("Partial APC data found", processedField.quality.message)
    }
    
    @Test
    fun `should handle empty count and type`() {
        val eventData = toEventData(
            "customAttr_APC_sensorer_antall" to "",
            "customAttr_APC_sensorer_type" to ""
        )
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        val apcInfo = processedField.value as APCInfoDto
        assertEquals(0, apcInfo.count)
        assertEquals("", apcInfo.type)
        assertEquals(QualityType.MISSING_VALUE, processedField.quality.type)
        assertEquals("No APC data found", processedField.quality.message)
    }
    
    @Test
    fun `should handle no APC data found`() {
        val result = processor.process(
            emptyMap(),
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        val apcInfo = processedField.value as APCInfoDto
        assertEquals(0, apcInfo.count)
        assertEquals("", apcInfo.type)
        assertEquals(QualityType.MISSING_VALUE, processedField.quality.type)
        assertEquals("No APC data found", processedField.quality.message)
    }
    
    @Test
    fun `should trim whitespace from type`() {
        val eventData = toEventData(
            "customAttr_APC_sensorer_antall" to "3",
            "customAttr_APC_sensorer_type" to "  Iris Irma Matrix  "
        )
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        val apcInfo = processedField.value as APCInfoDto
        assertEquals(3, apcInfo.count)
        assertEquals("Iris Irma Matrix", apcInfo.type) // Should be trimmed
        assertEquals(QualityType.VALID, processedField.quality.type)
    }

    @Test
    fun `should handle zero count as valid`() {
        val eventData = toEventData(
            "customAttr_APC_sensorer_antall" to "0",
            "customAttr_APC_sensorer_type" to "No Sensors"
        )
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        val apcInfo = processedField.value as APCInfoDto
        assertEquals(0, apcInfo.count)
        assertEquals("No Sensors", apcInfo.type)
        assertEquals(QualityType.VALID, processedField.quality.type)
    }
    
    @Test
    fun `should handle negative count by converting to zero`() {
        val eventData = toEventData(
            "customAttr_APC_sensorer_antall" to "-1",
            "customAttr_APC_sensorer_type" to "Invalid Count"
        )
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )
        
        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        val apcInfo = processedField.value as APCInfoDto
        assertEquals(0, apcInfo.count) // Negative should be converted to 0
        assertEquals("Invalid Count", apcInfo.type)
        assertEquals(QualityType.ENRICHED_VALUE, processedField.quality.type)
        assertEquals("Parsed APC count from '-1' to '0'", processedField.quality.message)
    }
}
