package no.ruter.kosi.fleet.unit.web

import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.verify
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobSchedulerManager
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.web.JobManagementController
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Instant

@WebMvcTest(JobManagementController::class, excludeAutoConfiguration = [SecurityAutoConfiguration::class])
class JobManagementControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockkBean
    private lateinit var jobService: JobService

    @MockkBean
    private lateinit var jobSchedulerManager: JobSchedulerManager

    @Test
    fun `GET jobs should return list of jobs with default pagination`() {
        val jobs = listOf(
            createTestJob(1, FleetJob.JobType.COMPLETE_IMPORT, FleetJob.JobStatus.COMPLETED),
            createTestJob(2, FleetJob.JobType.VEHICLE_RECONSTRUCTION, FleetJob.JobStatus.RUNNING)
        )
        every { jobService.findAll() } returns jobs

        mockMvc.perform(get("/jobs"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.jobs").isArray)
            .andExpect(jsonPath("$.jobs.length()").value(2))
            .andExpect(jsonPath("$.total").value(2))
            .andExpect(jsonPath("$.limit").value(20))
            .andExpect(jsonPath("$.offset").value(0))

        verify { jobService.findAll() }
    }

    @Test
    fun `GET jobs should filter by job type`() {
        val jobs = listOf(
            createTestJob(1, FleetJob.JobType.COMPLETE_IMPORT, FleetJob.JobStatus.COMPLETED),
            createTestJob(2, FleetJob.JobType.VEHICLE_RECONSTRUCTION, FleetJob.JobStatus.RUNNING)
        )
        every { jobService.findAll() } returns jobs

        mockMvc.perform(get("/jobs?type=COMPLETE_IMPORT"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.jobs.length()").value(1))
            .andExpect(jsonPath("$.jobs[0].type").value("COMPLETE_IMPORT"))
            .andExpect(jsonPath("$.total").value(1))

        verify { jobService.findAll() }
    }

    @Test
    fun `GET jobs should apply pagination`() {
        val jobs = (1..25).map { 
            createTestJob(it, FleetJob.JobType.COMPLETE_IMPORT, FleetJob.JobStatus.COMPLETED) 
        }
        every { jobService.findAll() } returns jobs

        mockMvc.perform(get("/jobs?limit=10&offset=5"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.jobs.length()").value(10))
            .andExpect(jsonPath("$.total").value(25))
            .andExpect(jsonPath("$.limit").value(10))
            .andExpect(jsonPath("$.offset").value(5))

        verify { jobService.findAll() }
    }

    @Test
    fun `GET jobs should handle service exception`() {
        every { jobService.findAll() } throws RuntimeException("Database error")

        mockMvc.perform(get("/jobs"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.error").value("Failed to list jobs: Database error"))

        verify { jobService.findAll() }
    }

    @Test
    fun `GET jobs by id should return job when found`() {
        val job = createTestJob(1, FleetJob.JobType.COMPLETE_IMPORT, FleetJob.JobStatus.COMPLETED)
        every { jobService.findJob(1) } returns job

        mockMvc.perform(get("/jobs/1"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.jobId").value(1))
            .andExpect(jsonPath("$.type").value("COMPLETE_IMPORT"))
            .andExpect(jsonPath("$.status").value("COMPLETED"))

        verify { jobService.findJob(1) }
    }

    @Test
    fun `GET jobs by id should return 404 when job not found`() {
        every { jobService.findJob(999) } returns null

        mockMvc.perform(get("/jobs/999"))
            .andExpect(status().isNotFound)

        verify { jobService.findJob(999) }
    }

    @Test
    fun `GET jobs by id should handle service exception`() {
        every { jobService.findJob(1) } throws RuntimeException("Database error")

        mockMvc.perform(get("/jobs/1"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.error").value("Failed to get job: Database error"))

        verify { jobService.findJob(1) }
    }

    @Test
    fun `GET job status should return status when job exists`() {
        val statusMap = mapOf(
            "jobId" to 1,
            "status" to "RUNNING",
            "progress" to 50.0
        )
        every { jobService.getJobStatus(1) } returns statusMap

        mockMvc.perform(get("/jobs/1/status"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.jobId").value(1))
            .andExpect(jsonPath("$.status").value("RUNNING"))
            .andExpect(jsonPath("$.progress").value(50.0))

        verify { jobService.getJobStatus(1) }
    }

    @Test
    fun `GET job status should handle service exception`() {
        every { jobService.getJobStatus(1) } throws RuntimeException("Job not found")

        mockMvc.perform(get("/jobs/1/status"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.error").value("Failed to get job status: Job not found"))

        verify { jobService.getJobStatus(1) }
    }

    @Test
    fun `POST cancel job should cancel job successfully`() {
        every { jobSchedulerManager.cancelJob(1) } returns true

        mockMvc.perform(post("/jobs/1/cancel"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.message").value("Job cancelled successfully."))

        verify { jobSchedulerManager.cancelJob(1) }
    }

    @Test
    fun `POST cancel job should return bad request when job not found`() {
        every { jobSchedulerManager.cancelJob(999) } returns false

        mockMvc.perform(post("/jobs/999/cancel"))
            .andExpect(status().isBadRequest)
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.message").value("No job found with ID 999."))

        verify { jobSchedulerManager.cancelJob(999) }
    }

    @Test
    fun `POST cancel job should handle service exception`() {
        every { jobSchedulerManager.cancelJob(1) } throws RuntimeException("Cancellation failed")

        mockMvc.perform(post("/jobs/1/cancel"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.message").value("Error cancelling job: Cancellation failed"))

        verify { jobSchedulerManager.cancelJob(1) }
    }

    private fun createTestJob(
        id: Int,
        type: FleetJob.JobType,
        status: FleetJob.JobStatus,
        manual: Boolean = true
    ): FleetJob {
        return FleetJob(
            jobId = id,
            type = type,
            status = status,
            manual = manual,
            quartzJobId = "test-job-$id",
            quartzJobGroup = "test-group",
            createdAt = Instant.now()
        )
    }
}
