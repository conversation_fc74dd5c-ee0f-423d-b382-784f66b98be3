package no.ruter.kosi.fleet.unit.web

import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.verify
import no.ruter.kosi.fleet.domain.job.JobSchedulerManager
import no.ruter.kosi.fleet.web.ReconstructionController
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
import org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
import org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration
import org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(
    ReconstructionController::class,
    excludeAutoConfiguration = [
        SecurityAutoConfiguration::class,
        SecurityFilterAutoConfiguration::class,
        UserDetailsServiceAutoConfiguration::class,
        OAuth2ClientAutoConfiguration::class,
        OAuth2ResourceServerAutoConfiguration::class
    ]
)
class ReconstructionControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockkBean
    private lateinit var jobSchedulerManager: JobSchedulerManager

    @Test
    fun `POST start reconstruction should start reconstruction successfully with default date`() {
        every { jobSchedulerManager.startReconstructionFromDate(any()) } returns true

        mockMvc.perform(post("/reconstructions/start"))
            .andExpect(status().isAccepted)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.message").value("Vehicle reconstruction from date started successfully"))
            .andExpect(jsonPath("$.status").value("started"))
            .andExpect(jsonPath("$.reconstructionDate").exists())

        verify { jobSchedulerManager.startReconstructionFromDate(any()) }
    }

    @Test
    fun `POST start reconstruction should start reconstruction successfully with custom date`() {
        val customDate = "2024-01-15T10:30:00Z"
        every { jobSchedulerManager.startReconstructionFromDate(customDate) } returns true

        mockMvc.perform(post("/reconstructions/start?fromInstant=$customDate"))
            .andExpect(status().isAccepted)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.message").value("Vehicle reconstruction from date started successfully"))
            .andExpect(jsonPath("$.reconstructionDate").value(customDate))
            .andExpect(jsonPath("$.status").value("started"))

        verify { jobSchedulerManager.startReconstructionFromDate(customDate) }
    }

    @Test
    fun `POST start reconstruction should return error when start fails`() {
        every { jobSchedulerManager.startReconstructionFromDate(any()) } returns false

        mockMvc.perform(post("/reconstructions/start"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.error").value("Failed to start reconstruction from date"))
            .andExpect(jsonPath("$.status").value("failed"))

        verify { jobSchedulerManager.startReconstructionFromDate(any()) }
    }

    @Test
    fun `POST start reconstruction should handle service exception`() {
        every { jobSchedulerManager.startReconstructionFromDate(any()) } throws RuntimeException("Scheduler error")

        mockMvc.perform(post("/reconstructions/start"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.error").value("Internal server error: Scheduler error"))
            .andExpect(jsonPath("$.status").value("error"))

        verify { jobSchedulerManager.startReconstructionFromDate(any()) }
    }

    @Test
    fun `POST start reconstruction should handle invalid date format gracefully`() {
        val invalidDate = "invalid-date-format"
        every { jobSchedulerManager.startReconstructionFromDate(invalidDate) } returns true

        mockMvc.perform(post("/reconstructions/start?fromInstant=$invalidDate"))
            .andExpect(status().isAccepted)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.message").value("Vehicle reconstruction from date started successfully"))
            .andExpect(jsonPath("$.reconstructionDate").value(invalidDate))
            .andExpect(jsonPath("$.status").value("started"))

        verify { jobSchedulerManager.startReconstructionFromDate(invalidDate) }
    }

    @Test
    fun `POST start reconstruction should handle empty date parameter`() {
        every { jobSchedulerManager.startReconstructionFromDate(any()) } returns true

        mockMvc.perform(post("/reconstructions/start?fromInstant="))
            .andExpect(status().isAccepted)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.message").value("Vehicle reconstruction from date started successfully"))
            .andExpect(jsonPath("$.status").value("started"))

        verify { jobSchedulerManager.startReconstructionFromDate(any()) }
    }

    @Test
    fun `POST start reconstruction should handle concurrent reconstruction attempt`() {
        every { jobSchedulerManager.startReconstructionFromDate(any()) } returns false

        mockMvc.perform(post("/reconstructions/start"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.error").value("Failed to start reconstruction from date"))
            .andExpect(jsonPath("$.status").value("failed"))

        verify { jobSchedulerManager.startReconstructionFromDate(any()) }
    }

    @Test
    fun `POST start reconstruction should handle service timeout exception`() {
        every { jobSchedulerManager.startReconstructionFromDate(any()) } throws 
            RuntimeException("Timeout waiting for scheduler")

        mockMvc.perform(post("/reconstructions/start"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.error").value("Internal server error: Timeout waiting for scheduler"))
            .andExpect(jsonPath("$.status").value("error"))

        verify { jobSchedulerManager.startReconstructionFromDate(any()) }
    }
}
