package no.ruter.kosi.fleet.unit.application.services

import io.mockk.Runs
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import junit.framework.TestCase.assertEquals
import junit.framework.TestCase.assertFalse
import junit.framework.TestCase.assertTrue
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobSchedulerManager
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraJobRepository
import no.ruter.kosi.fleet.infrastructure.quartz.CompleteImportJob
import no.ruter.kosi.fleet.infrastructure.quartz.ImportSchedulerProperties
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.quartz.Job
import org.quartz.JobKey
import org.quartz.Scheduler
import org.quartz.Trigger

class JobSchedulerManagerTest {
    private val jobService = mockk<JobService>()
    private val jobRepository = mockk<AuroraJobRepository>()
    private val scheduler = mockk<Scheduler>()
    private val importSchedulerProperties = mockk<ImportSchedulerProperties>()

    private lateinit var jobSchedulerManager: JobSchedulerManager

    @BeforeEach
    fun setup() {
        clearAllMocks()

        jobSchedulerManager =
            JobSchedulerManager(
                jobService = jobService,
                scheduler = scheduler,
                importSchedulerProperties = importSchedulerProperties,
            )
    }

    @Test
    fun `initializeScheduler should schedule periodic imports when enabled`() {
        every { importSchedulerProperties.enabled } returns true
        every { importSchedulerProperties.cron } returns "0 0 * * * ?"

        slot<Job>()
        every {
            jobService.createJob(
                type = FleetJob.JobType.COMPLETE_IMPORT,
                manual = false,
                quartzJobId = any(),
                quartzJobGroup = any(),
            )
        } returns
            mockk<FleetJob> {
                every { jobId } returns 1
            }

        every { jobService.deschedulePendingOrScheluduledJobs() } just runs

        every { scheduler.scheduleJob(any(), any<Trigger>()) } returns mockk()

        jobSchedulerManager.initializeScheduler()

        verify {
            scheduler.scheduleJob(
                match { it.key == JobKey.jobKey("scheduledCompleteImport", "imports") },
                any<Trigger>(),
            )
        }
    }

    @Test
    fun `initializeScheduler should not schedule periodic imports when disabled`() {
        every { importSchedulerProperties.enabled } returns false

        jobSchedulerManager.initializeScheduler()

        verify(exactly = 0) { scheduler.scheduleJob(any(), any()) }
    }

    @Test
    fun `cancelJob returns true when job exists`() {
        val job =
            mockk<FleetJob> {
                every { jobId } returns 1
                every { quartzJobId } returns "jobId"
                every { quartzJobGroup } returns "jobGroup"
            }

        every { jobRepository.findById(1) } returns job
        every { jobService.cancelJob(1) } just Runs
        every { jobService.findJob(1) } returns job

        every { scheduler.currentlyExecutingJobs } returns emptyList()
        every { scheduler.deleteJob(any()) } returns true

        val result = jobSchedulerManager.cancelJob(1)

        assertTrue(result)
        verify { jobService.cancelJob(1) }
        verify { scheduler.deleteJob(JobKey.jobKey("jobId", "jobGroup")) }
    }

    @Test
    fun `cancelJob returns false when job does not exist`() {
        every { jobRepository.findById(1) } returns null
        every { jobService.findJob(1) } returns null

        val result = jobSchedulerManager.cancelJob(1)

        assertFalse(result)
        verify(exactly = 0) { jobService.cancelJob(any()) }
        verify(exactly = 0) { scheduler.deleteJob(any()) }
    }

    @Test
    fun `cancelActiveImport returns true when active imports exist`() {
        val job =
            mockk<FleetJob> {
                every { jobId } returns 1
                every { type } returns FleetJob.JobType.COMPLETE_IMPORT
                every { parentJobId } returns null
            }

        every { jobService.findActiveJobs() } returns listOf(job)

        val spyJobSchedulerManager = spyk(jobSchedulerManager)
        every { spyJobSchedulerManager.cancelJob(1) } returns true

        val result = spyJobSchedulerManager.cancelActiveImport()

        assertTrue(result)
        verify { spyJobSchedulerManager.cancelJob(1) }
    }

    @Test
    fun `cancelActiveImport returns false when no active imports exist`() {
        every { jobService.findActiveJobs() } returns emptyList()

        val result = jobSchedulerManager.cancelActiveImport()

        assertFalse(result)
    }

    @Test
    fun `isAnyImportInProgress returns true when active import exists`() {
        every { jobService.findActiveJobs() } returns
            listOf(
                mockk {
                    every { type } returns FleetJob.JobType.COMPLETE_IMPORT
                },
            )

        val result = jobSchedulerManager.isAnyImportInProgress()

        assertTrue(result)
    }

    @Test
    fun `isAnyImportInProgress returns false when no active import exists`() {
        every { jobService.findActiveJobs() } returns emptyList()

        val result = jobSchedulerManager.isAnyImportInProgress()

        assertFalse(result)
    }

    @Test
    fun `getImportProgress returns active job status when active job exists`() {
        val job =
            mockk<FleetJob> {
                every { jobId } returns 1
                every { type } returns FleetJob.JobType.COMPLETE_IMPORT
            }

        every { jobService.findActiveJobs() } returns listOf(job)

        val statusMap = mapOf("status" to "RUNNING", "progressPercentage" to 50.0)
        every { jobService.getJobStatus(1) } returns statusMap

        val result = jobSchedulerManager.getImportProgress()

        assertEquals(statusMap, result)
    }

    @Test
    fun `getImportProgress returns recent job status with inProgress false when no active job exists but recent job exists`() {
        every { jobService.findActiveJobs() } returns emptyList()

        val recentJob =
            mockk<FleetJob> {
                every { jobId } returns 1
                every { type } returns FleetJob.JobType.COMPLETE_IMPORT
            }

        every { jobService.findLatestJobByType(FleetJob.JobType.COMPLETE_IMPORT) } returns recentJob

        val statusMap = mapOf("status" to "COMPLETED", "progressPercentage" to 100.0)
        every { jobService.getJobStatus(1) } returns statusMap

        val result = jobSchedulerManager.getImportProgress()

        assertEquals(statusMap + mapOf("inProgress" to false), result)
    }

    @Test
    fun `getImportProgress returns default message when no jobs found`() {
        every { jobService.findActiveJobs() } returns emptyList()
        every { jobService.findLatestJobByType(FleetJob.JobType.COMPLETE_IMPORT) } returns null

        val result = jobSchedulerManager.getImportProgress()

        assertEquals(false, result["inProgress"])
        assertTrue(result.containsKey("message"))
        assertEquals("No import in progress or history found.", result["message"])
    }

    @Test
    fun `scheduleJob successfully creates and schedules a job`() {
        val parentFleetJob =
            mockk<FleetJob> {
                every { jobId } returns 1
                every { type } returns FleetJob.JobType.COMPLETE_IMPORT
                every { manual } returns true
            }

        val childJob =
            mockk<FleetJob> {
                every { jobId } returns 2
                every { type } returns FleetJob.JobType.IMPORT_FRIDA_VEHICLES
                every { quartzJobId } returns "frida-job-id"
                every { quartzJobGroup } returns "frida-job-group"
            }

        every {
            jobService.createChildJob(
                parentFleetJob = parentFleetJob,
                type = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
                quartzJobId = any(),
                quartzJobGroup = any(),
            )
        } returns childJob

        every { scheduler.scheduleJob(any(), any<Trigger>()) } returns mockk()

        val result =
            jobSchedulerManager.scheduleJob(
                parentFleetJob = parentFleetJob,
                fleetJobType = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
                jobClass = CompleteImportJob::class.java,
            )

        assertEquals(childJob, result)
        verify {
            scheduler.scheduleJob(
                match { it.key == JobKey.jobKey("frida-job-id", "frida-job-group") },
                any<Trigger>(),
            )
        }
    }

    @Test
    fun `scheduleJob handles exceptions by failing the job`() {
        val parentFleetJob =
            mockk<FleetJob> {
                every { jobId } returns 1
                every { type } returns FleetJob.JobType.COMPLETE_IMPORT
                every { manual } returns true
            }

        val childJob =
            mockk<FleetJob> {
                every { jobId } returns 2
                every { type } returns FleetJob.JobType.IMPORT_FRIDA_VEHICLES
                every { quartzJobId } returns "frida-job-id"
                every { quartzJobGroup } returns "frida-job-group"
            }

        every {
            jobService.createChildJob(
                parentFleetJob = parentFleetJob,
                type = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
                quartzJobId = any(),
                quartzJobGroup = any(),
            )
        } returns childJob

        every { scheduler.scheduleJob(any(), any<Trigger>()) } throws RuntimeException("Scheduling error")
        every { jobService.failJob(2, any()) } just Runs

        assertThrows(RuntimeException::class.java) {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = parentFleetJob,
                fleetJobType = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
                jobClass = CompleteImportJob::class.java,
            )
        }

        verify { jobService.failJob(2, match { it.contains("Scheduling error") }) }
    }
}
