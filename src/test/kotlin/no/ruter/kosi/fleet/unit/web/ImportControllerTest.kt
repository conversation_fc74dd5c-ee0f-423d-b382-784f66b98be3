package no.ruter.kosi.fleet.unit.web

import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.verify
import no.ruter.kosi.fleet.domain.job.JobSchedulerManager
import no.ruter.kosi.fleet.web.ImportController
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Instant

@WebMvcTest(ImportController::class, excludeAutoConfiguration = [SecurityAutoConfiguration::class])
class ImportControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockkBean
    private lateinit var jobSchedulerManager: JobSchedulerManager

    @Test
    fun `GET import progress should return progress when import is active`() {
        val progressMap = mapOf(
            "inProgress" to true,
            "jobId" to 123,
            "status" to "RUNNING",
            "progress" to 75.5,
            "message" to "Processing vehicles..."
        )
        every { jobSchedulerManager.getImportProgress() } returns progressMap

        mockMvc.perform(get("/imports/progress"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.inProgress").value(true))
            .andExpect(jsonPath("$.jobId").value(123))
            .andExpect(jsonPath("$.status").value("RUNNING"))
            .andExpect(jsonPath("$.progress").value(75.5))
            .andExpect(jsonPath("$.message").value("Processing vehicles..."))

        verify { jobSchedulerManager.getImportProgress() }
    }

    @Test
    fun `GET import progress should return no progress when no import is active`() {
        val progressMap = mapOf(
            "inProgress" to false,
            "message" to "No import in progress or history found."
        )
        every { jobSchedulerManager.getImportProgress() } returns progressMap

        mockMvc.perform(get("/imports/progress"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.inProgress").value(false))
            .andExpect(jsonPath("$.message").value("No import in progress or history found."))

        verify { jobSchedulerManager.getImportProgress() }
    }

    @Test
    fun `GET import progress should handle service exception`() {
        every { jobSchedulerManager.getImportProgress() } throws RuntimeException("Database connection failed")

        mockMvc.perform(get("/imports/progress"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.error").value("Failed to fetch import progress: Database connection failed"))

        verify { jobSchedulerManager.getImportProgress() }
    }

    @Test
    fun `POST cancel import should cancel active import successfully`() {
        every { jobSchedulerManager.cancelActiveImport() } returns true

        mockMvc.perform(post("/imports/cancel"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.message").value("Active import cancelled successfully."))

        verify { jobSchedulerManager.cancelActiveImport() }
    }

    @Test
    fun `POST cancel import should return bad request when no active import`() {
        every { jobSchedulerManager.cancelActiveImport() } returns false

        mockMvc.perform(post("/imports/cancel"))
            .andExpect(status().isBadRequest)
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.message").value("No active import to cancel."))

        verify { jobSchedulerManager.cancelActiveImport() }
    }

    @Test
    fun `POST cancel import should handle service exception`() {
        every { jobSchedulerManager.cancelActiveImport() } throws RuntimeException("Cancellation failed")

        mockMvc.perform(post("/imports/cancel"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.message").value("Error cancelling import: Cancellation failed"))

        verify { jobSchedulerManager.cancelActiveImport() }
    }

    @Test
    fun `POST start import should start import successfully with default date`() {
        every { jobSchedulerManager.startImportFromDate(any()) } returns true

        mockMvc.perform(post("/imports/start"))
            .andExpect(status().isAccepted)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.message").value("Import from date started successfully"))
            .andExpect(jsonPath("$.status").value("started"))
            .andExpect(jsonPath("$.importDate").exists())

        verify { jobSchedulerManager.startImportFromDate(any()) }
    }

    @Test
    fun `POST start import should start import successfully with custom date`() {
        val customDate = "2024-01-15T10:30:00Z"
        every { jobSchedulerManager.startImportFromDate(customDate) } returns true

        mockMvc.perform(post("/imports/start?fromInstant=$customDate"))
            .andExpect(status().isAccepted)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.message").value("Import from date started successfully"))
            .andExpect(jsonPath("$.importDate").value(customDate))
            .andExpect(jsonPath("$.status").value("started"))

        verify { jobSchedulerManager.startImportFromDate(customDate) }
    }

    @Test
    fun `POST start import should return error when start fails`() {
        every { jobSchedulerManager.startImportFromDate(any()) } returns false

        mockMvc.perform(post("/imports/start"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.error").value("Failed to start import from date"))
            .andExpect(jsonPath("$.status").value("failed"))

        verify { jobSchedulerManager.startImportFromDate(any()) }
    }

    @Test
    fun `POST start import should handle service exception`() {
        every { jobSchedulerManager.startImportFromDate(any()) } throws RuntimeException("Scheduler error")

        mockMvc.perform(post("/imports/start"))
            .andExpect(status().isInternalServerError)
            .andExpect(jsonPath("$.error").value("Internal server error: Scheduler error"))
            .andExpect(jsonPath("$.status").value("error"))

        verify { jobSchedulerManager.startImportFromDate(any()) }
    }
}
