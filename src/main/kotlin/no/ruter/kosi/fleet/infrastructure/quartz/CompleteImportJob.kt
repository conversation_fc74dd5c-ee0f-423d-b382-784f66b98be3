package no.ruter.kosi.fleet.infrastructure.quartz

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobSchedulerManager
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.Instant

@DisallowConcurrentExecution
@Component
class CompleteImportJob(
    override val jobService: JobService,
    private val jobSchedulerManager: JobSchedulerManager
) : BaseJob() {

    sealed class CompleteImportJobError {
        data object JobLookupError : CompleteImportJobError()
        data object JobTransitionError : CompleteImportJobError()
        data object JobSchedulerError : CompleteImportJobError()
        data object JobCancelled : CompleteImportJobError()
        data object Timeout : CompleteImportJobError()
    }

    override fun executeInternal(context: JobExecutionContext, fleetJob: FleetJob?) {
        val jobKey = context.jobDetail.key
        val jobDataMap = context.mergedJobDataMap
        val jobId: Int? = if (jobDataMap.containsKey("jobId")) jobDataMap.getInt("jobId") else null
        val importDate = getImportDateFromContext(context)
        val jobType = jobDataMap.getString("jobType") ?: "MANUAL"

        logger.info("execute called for jobKey: $jobKey, jobId: $jobId, importDate: $importDate, jobType: $jobType")
        MdcUtils.setJobName(jobKey.name)
        MdcUtils.setJobId(jobId)

        if (jobId == null) {
            logger.error("jobId is null or missing from JobDataMap, cannot proceed!")
            logger.error("JobDataMap contents: ${jobDataMap.toMap()}")
            return
        }
        val job = jobService.findJob(jobId)
        if (job == null) {
            logger.error("No job found for jobId: $jobId")
            return
        } else {
            logger.info("Found job: $job")
        }
        logger.info("Starting job orchestration for jobId: $jobId, isCurrent status: ${job.status}")
        if (job.status == FleetJob.JobStatus.CANCELLED) {
            logger.info("CompleteImportJob: parent job {} already CANCELLED", jobId)
            jobService.cancelJob(jobId)
            return
        }
        val errors = mutableListOf<CompleteImportJobError>()
        listOf(
            FleetJob.JobType.IMPORT_FRIDA_CONTRACTS to FridaContractsImportJob::class.java,
            FleetJob.JobType.IMPORT_FRIDA_VEHICLES to FridaVehicleImportJob::class.java,
            FleetJob.JobType.IMPORT_AUTOSYS to AutosysImportJob::class.java,
            FleetJob.JobType.EVENTDATA_VALIDATION to EventDataValidationJob::class.java,
            FleetJob.JobType.VEHICLE_RECONSTRUCTION to ReconstructVehicleJob::class.java,
            FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA to PublishVehiclesToKafkaJob::class.java,
            FleetJob.JobType.VEHICLE_PUBLISHING_SNOWFLAKE to PublishVehiclesToSnowflakeJob::class.java,
        ).forEach { (jobType, klass) ->
            MdcUtils.setJobName(jobType.name) // overide with child job name
            val res = scheduleAndWait(job, jobType, klass, importDate)
            res.fold(
                { err ->
                    errors += err
                    logger.error("error: {}", err)
                },
                {
                    logger.info("completed")
                }
            )
        }

        MdcUtils.setJobName(jobKey.name)
        if (errors.any()) {
            errors.forEach { logger.error("Job error: {}", it) }
            if (errors.any { it is CompleteImportJobError.JobCancelled }) {
                jobService.cancelJob(jobId)
                logger.info("Parent job $jobId cancelled due to child cancellation")
            } else {
                jobService.failJob(jobId, errors.joinToString())
            }
        } else {
            logger.info("CompleteImportJob executed successfully")
            jobService.completeJob(jobId)
        }
    }

    private fun scheduleAndWait(
        parentJob: FleetJob,
        fleetJobType: FleetJob.JobType,
        jobClass: Class<out Job>,
        importDate: Instant?
    ): Either<CompleteImportJobError, FleetJob> {
        return try {
            val scheduledJob = jobSchedulerManager.scheduleJob(parentJob, fleetJobType, jobClass, importDate)
            val pollIntervalMs = 200L
            var completedJob: FleetJob?
            while (true) {
                completedJob = jobService.findJob(scheduledJob.jobId)
                if (completedJob != null &&
                    (completedJob.status == FleetJob.JobStatus.COMPLETED ||
                            completedJob.status == FleetJob.JobStatus.FAILED ||
                            completedJob.status == FleetJob.JobStatus.CANCELLED)
                ) {
                    break
                }
                Thread.sleep(pollIntervalMs)
            }
            if (completedJob == null) {
                logger.error("scheduleAndWait: Could not find job after scheduling (jobId=${scheduledJob.jobId})")
                return CompleteImportJobError.JobLookupError.left()
            }
            return when (completedJob.status) {
                FleetJob.JobStatus.FAILED -> {
                    logger.error("scheduleAndWait: Child job ${scheduledJob.jobId} FAILED. Last error: ${completedJob.lastError}")
                    if (completedJob.lastError?.contains("JobCancelledException") == true) {
                        CompleteImportJobError.JobCancelled.left()
                    } else CompleteImportJobError.JobTransitionError.left()
                }

                FleetJob.JobStatus.COMPLETED -> completedJob.right()
                else -> {
                    logger.error("scheduleAndWait: Child job ${scheduledJob.jobId} in unexpected status: ${completedJob.status}")
                    CompleteImportJobError.JobTransitionError.left()
                }
            }
        } catch (e: JobCancelledException) {
            CompleteImportJobError.JobCancelled.left()
        } catch (e: Exception) {
            logger.error("scheduleAndWait: Exception while scheduling/waiting: ${e.message}", e)
            CompleteImportJobError.JobSchedulerError.left()
        }
    }

    private fun getImportDateFromContext(context: JobExecutionContext): Instant? {
        val jobDataMap = context.mergedJobDataMap
        return if (jobDataMap.containsKey("importDate")) {
            try {
                val value = jobDataMap["importDate"]
                when (value) {
                    is Long -> Instant.ofEpochMilli(value)
                    is String -> Instant.parse(value)
                    else -> null
                }
            } catch (e: Exception) {
                logger.warn("Error parsing importDate from job data: {}", e.message)
                null
            }
        } else {
            null
        }
    }
}
