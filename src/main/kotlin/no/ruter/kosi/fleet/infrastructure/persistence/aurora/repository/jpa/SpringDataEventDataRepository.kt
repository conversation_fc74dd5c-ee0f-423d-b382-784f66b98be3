package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.scd2.SCD2Repository
import no.ruter.kosi.fleet.domain.scd2.SCD2RepositoryBase
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.Instant

@Repository
interface SpringDataEventDataRepository : SCD2RepositoryBase<EventData, Int, String> {
    
    @Query(
        """
        SELECT e FROM EventData e
        WHERE (:vehicleId IS NULL OR e.event.vehicle.vehicleId = :vehicleId)
        AND (:vehicleRef IS NULL OR e.event.vehicle.vehicleRef = :vehicleRef)
        AND (:fieldName IS NULL OR e.fieldName = :fieldName)
        AND (:fieldValue IS NULL OR e.fieldValue = :fieldValue)
        AND (:isCurrent IS NULL OR e.isCurrent = :isCurrent)
    """
    )
    fun findWithFiltersNoDates(
        @Param("vehicleId") vehicleId: Int?,
        @Param("vehicleRef") vehicleRef: String?,
        @Param("fieldName") fieldName: String?,
        @Param("fieldValue") fieldValue: String?,
        @Param("isCurrent") isCurrent: Boolean?,
        pageable: Pageable
    ): Page<EventData>
    
    @Query(
        """
        SELECT e FROM EventData e
        WHERE (:vehicleId IS NULL OR e.event.vehicle.vehicleId = :vehicleId)
        AND (:vehicleRef IS NULL OR e.event.vehicle.vehicleRef = :vehicleRef)
        AND (:fieldName IS NULL OR e.fieldName = :fieldName)
        AND (:fieldValue IS NULL OR e.fieldValue = :fieldValue)
        AND e.effectiveFrom >= :startDate
        AND (:isCurrent IS NULL OR e.isCurrent = :isCurrent)
    """
    )
    fun findWithStartDate(
        @Param("vehicleId") vehicleId: Int?,
        @Param("vehicleRef") vehicleRef: String?,
        @Param("fieldName") fieldName: String?,
        @Param("fieldValue") fieldValue: String?,
        @Param("startDate") startDate: Instant,
        @Param("isCurrent") isCurrent: Boolean?,
        pageable: Pageable
    ): Page<EventData>
    
    @Query(
        """
        SELECT e FROM EventData e
        WHERE (:vehicleId IS NULL OR e.event.vehicle.vehicleId = :vehicleId)
        AND (:vehicleRef IS NULL OR e.event.vehicle.vehicleRef = :vehicleRef)
        AND (:fieldName IS NULL OR e.fieldName = :fieldName)
        AND (:fieldValue IS NULL OR e.fieldValue = :fieldValue)
        AND e.effectiveFrom <= :endDate
        AND (:isCurrent IS NULL OR e.isCurrent = :isCurrent)
    """
    )
    fun findWithEndDate(
        @Param("vehicleId") vehicleId: Int?,
        @Param("vehicleRef") vehicleRef: String?,
        @Param("fieldName") fieldName: String?,
        @Param("fieldValue") fieldValue: String?,
        @Param("endDate") endDate: Instant,
        @Param("isCurrent") isCurrent: Boolean?,
        pageable: Pageable
    ): Page<EventData>
    
    @Query(
        """
        SELECT e FROM EventData e
        WHERE (:vehicleId IS NULL OR e.event.vehicle.vehicleId = :vehicleId)
        AND (:vehicleRef IS NULL OR e.event.vehicle.vehicleRef = :vehicleRef)
        AND (:fieldName IS NULL OR e.fieldName = :fieldName)
        AND (:fieldValue IS NULL OR e.fieldValue = :fieldValue)
        AND e.effectiveFrom >= :startDate
        AND e.effectiveFrom <= :endDate
        AND (:isCurrent IS NULL OR e.isCurrent = :isCurrent)
    """
    )
    fun findWithDateRange(
        @Param("vehicleId") vehicleId: Int?,
        @Param("vehicleRef") vehicleRef: String?,
        @Param("fieldName") fieldName: String?,
        @Param("fieldValue") fieldValue: String?,
        @Param("startDate") startDate: Instant,
        @Param("endDate") endDate: Instant,
        @Param("isCurrent") isCurrent: Boolean?,
        pageable: Pageable
    ): Page<EventData>

    fun findByEventVehicleVehicleId(vehicleId: Int): List<EventData>


    @Query(
        """
        SELECT e
        FROM EventData e
        WHERE e.event.vehicle.vehicleId = :vehicleId
        AND e.isCurrent = true
        """,
    )
    fun findCurrentByVehicleId(
        @Param("vehicleId") vehicleId: Int,
    ): List<EventData>

    @Query(
        """
        SELECT e.fieldName, e.fieldValue
        FROM EventData e
        WHERE e.event.vehicle.vehicleId = :vehicleId
        AND e.isCurrent = true
        """,
    )
    fun findCurrentFieldValuesByVehicleId(
        @Param("vehicleId") vehicleId: Int,
    ): List<Array<Any?>>

    @Query(
        """
        SELECT e
        FROM EventData e
        WHERE e.event.vehicle.vehicleRef = :vehicleRef
        AND e.isCurrent = true
        """,
    )
    fun findCurrentByVehicleRef(
        @Param("vehicleRef") vehicleRef: String,
    ): List<EventData>

    @Query(
        """
        SELECT e
        FROM EventData e
        WHERE e.event.vehicle.vehicleId = :vehicleId
        ORDER BY e.effectiveFrom ASC, e.fieldName ASC
        """,
    )
    fun findAllHistoricalByVehicleId(
        @Param("vehicleId") vehicleId: Int,
    ): List<EventData>
}