package no.ruter.kosi.fleet.domain.vehicle

import com.fasterxml.jackson.databind.ObjectMapper
import kotlin.reflect.KClass
import no.ruter.kosi.fleet.domain.vehicle.dto.VehicleDto
import no.ruter.kosi.fleet.domain.quality.dto.VehicleQualityDto
import no.ruter.kosi.fleet.domain.event.DataSource
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.quality.dto.VehicleFieldQualityDto
import no.ruter.kosi.fleet.domain.quarantine.QuarantineService
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessorRegistry
import no.ruter.kosi.fleet.domain.vehicle.processors.common.getErrorMessage
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Service
class VehicleReconstructionService(
    private val eventDataRepository: AuroraEventDataRepository,
    private val internalVehicleRepository: AuroraInternalVehicleRepository,
    private val snapshotRepository: AuroraVehicleRepository,
    private val fieldProcessorRegistry: VehicleFieldProcessorRegistry,
    private val quarantineService: QuarantineService,
    private val objectMapper: ObjectMapper,
) {
    private val logger = LoggerFactory.getLogger(VehicleReconstructionService::class.java)

    @Transactional
    fun reconstructVehicle(
        vehicleId: Int,
        timestamp: Instant? = null,
    ): Pair<VehicleDto, VehicleQualityDto>? {
        val eventDataList = eventDataRepository.findByVehicleId(vehicleId)
        val lastSnapshot = snapshotRepository.findCurrentById(vehicleId) // .bind()
        val result = reconstructVehicleFromEventData(vehicleId, eventDataList, lastSnapshot, timestamp) ?: return null
        return result
    }

    @Transactional
    fun reconstructVehicle(
        vehicle: InternalVehicle,
        timestamp: Instant? = null,
    ): Pair<VehicleDto, VehicleQualityDto>? {
        val vehicleId = vehicle.vehicleId
        val eventDataList = eventDataRepository.findByVehicleId(vehicleId)
        val lastSnapshot = snapshotRepository.findCurrentById(vehicleId) // .bind()
        val result = reconstructVehicleFromEventData(vehicleId, eventDataList, lastSnapshot, timestamp) ?: return null
        return result
    }

    @Transactional
    fun reconstructVehicle(
        vehicleRef: String,
        timestamp: Instant? = null,
    ): Pair<VehicleDto, VehicleQualityDto>? {
        val vehicleId =
            internalVehicleRepository.findByVehicleRef(vehicleRef)?.vehicleId
                ?: throw IllegalStateException("vehicle $vehicleRef not found")
        val eventDataList = eventDataRepository.findByVehicleRef(vehicleRef)
        val lastSnapshot = snapshotRepository.findCurrentByVehicleRef(vehicleRef) // .bind()
        val result = reconstructVehicleFromEventData(vehicleId, eventDataList, lastSnapshot, timestamp) ?: return null
        return result
    }

    @Transactional
    fun reconstructVehicleFromEventData(
        vehicleId: Int,
        eventDataList: List<EventData>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant? = null,
    ): Pair<VehicleDto, VehicleQualityDto>? {
        val filteredEventData =
            timestamp?.let { ts ->
                // for historical reconstruction, filter by effectiveFrom (historic date)
                // not by event.timestamp (import date)
                val filtered = eventDataList.filter { it.effectiveFrom <= ts }
                logger.debug("Filtered EventData for timestamp {}: {} -> {} records",
                    ts, eventDataList.size, filtered.size)
                filtered
            } ?: eventDataList

        val aggregatedEventData = aggregateLatestEventData(filteredEventData)

        // clear quarantined data for this vehicle
        if (eventDataList.isNotEmpty()) {
            quarantineService.clearQuarantineForVehicle(vehicleId)
        }

        val orderedProcessors = fieldProcessorRegistry.getProcessors()
        val dependencyMap = fieldProcessorRegistry.getDeclaredDependencies()

        val resultsByClass = mutableMapOf<KClass<out VehicleFieldProcessor>, ProcessedField>()
        val resultsByPath = mutableMapOf<String, ProcessedField>()

        val processingErrors = mutableMapOf<KClass<out VehicleFieldProcessor>, ProcessingError>()

        for (proc in orderedProcessors) {
            val procClass = proc::class
            val neededClasses = dependencyMap[procClass] ?: emptyList()
            val missingClasses = neededClasses.filter { it !in resultsByClass.keys }
            val erroredClasses = neededClasses.filter { it in processingErrors.keys }

            if (missingClasses.isNotEmpty() || erroredClasses.isNotEmpty()) {
                val missingNames = missingClasses.joinToString(", ") { it.simpleName ?: "<anonymous>" }
                val erroredNames = erroredClasses.joinToString(", ") { it.simpleName ?: "<anonymous>" }
                val reason = when {
                    missingClasses.isNotEmpty() -> "Missing inputs: $missingNames"
                    else -> "Dependencies errored: $erroredNames"
                }
                logger.warn("Processor '${procClass.simpleName}' has incomplete inputs ($reason), running anyway")
            }

            val result = proc.process(aggregatedEventData, resultsByClass, lastSnapshot, timestamp)
            result.fold(
                { err ->
                    processingErrors[procClass] = err
                    logger.debug("${procClass.simpleName} failed: ${err.getErrorMessage()}")
                },
                { pf ->
                    resultsByClass[procClass] = pf
                    proc.jsonPath?.let { resultsByPath[it] = pf } // store by jsonPath too
                    logger.debug("${procClass.simpleName} succeeded")
                }
            )
        }

        // quarantine fields with errors
        processingErrors.forEach { (procClass, error) ->
            // try to find eventData by processor name if possible, else log
            val field = procClass.simpleName ?: "<anonymous>"
            val eventData = aggregatedEventData[field]
            if (eventData != null) {
                quarantineService.quarantineProcessingError(eventData.event, field, error)
                logger.warn("Error processing field '$field': ${error.getErrorMessage()}")
            } else {
                logger.warn("Error processing field '$field' but no event data found: ${error.getErrorMessage()}")
            }
        }

        val outputResults: Map<String, ProcessedField> = orderedProcessors
            .mapNotNull { proc ->
                val pf = resultsByClass[proc::class] ?: return@mapNotNull null
                proc.jsonPath?.let { jsonPath ->
                    jsonPath to pf
                }
            }
            .toMap()

        // use objectmapper to copy the nested output map directly to the object, don't like this but it works for now and saves me a lot of mapping
        val nested: Map<String, Any?> = outputResults.toNestedValueMap().plus(
            "effectiveFrom" to Instant.now(),
        )
        val vehicleDto: VehicleDto = objectMapper.convertValue(nested, VehicleDto::class.java)
        val vehicleDtoWithId =
            vehicleDto.copy(
                vehicleId = 0,
                internalVehicleId = vehicleId,
                identification = vehicleDto.identification.copy(
                    vehicleId = vehicleId
                )
            ) // TODO fix this later, bit ugly
        val vehicleQualityDto = createVehicleQualityDto(vehicleId, resultsByClass)

        return Pair(vehicleDtoWithId, vehicleQualityDto)
    }

    private fun createVehicleQualityDto(
        vehicleId: Int,
        resultsByClass: Map<KClass<out VehicleFieldProcessor>, ProcessedField>
    ): VehicleQualityDto {
        val now = Instant.now()
        val fq = resultsByClass.map { (procClass, pf) ->
            VehicleFieldQualityDto(
                id = 0,
                fieldName = procClass.simpleName ?: "<anonymous>",
                fieldValue = pf.value?.toString(),
                qualityType = pf.quality.type.name,
                message = pf.quality.message,
                effectiveFrom = now,
                effectiveTo = null,
                isCurrent = true,
            )
        }
        return VehicleQualityDto(
            id             = 0,
            vehicleId      = vehicleId,
            effectiveFrom  = now,
            effectiveTo    = null,
            isCurrent      = true,
            fieldQualities = fq
        )
    }

    fun Map<String,ProcessedField>.toNestedValueMap(): Map<String,Any?> {
        val root = mutableMapOf<String,Any?>()
        for ((path, pf) in this) {
            val segments = path.split('.')
            // do the intermediate maps
            var curr: MutableMap<String,Any?> = root
            for (i in 0 until segments.size - 1) {
                val seg = segments[i]
                @Suppress("UNCHECKED_CAST")
                curr = curr.computeIfAbsent(seg) { mutableMapOf<String,Any?>() } as MutableMap<String,Any?>
            }
            // raw value into last segment
            curr[segments.last()] = pf.value
        }
        return root
    }

    private fun aggregateLatestEventData(eventDataList: List<EventData>): Map<String, EventData?> =
        eventDataList
            .groupBy { it.fieldName }
            .mapValues { (_, fieldData) ->
                // for each field, fin:wd the event data record that comes from the highest priority source,
                fieldData
                    .maxWithOrNull(
                        compareBy({ DataSource.valueOf(it.event.source).priority }, { it.event.timestamp }),
                    )
            }
}