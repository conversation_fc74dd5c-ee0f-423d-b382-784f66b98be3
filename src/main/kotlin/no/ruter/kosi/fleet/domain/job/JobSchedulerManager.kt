package no.ruter.kosi.fleet.domain.job

import jakarta.annotation.PostConstruct
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import no.ruter.kosi.fleet.infrastructure.quartz.CompleteImportJob
import no.ruter.kosi.fleet.infrastructure.quartz.FridaHistoricVehicleImportJob
import no.ruter.kosi.fleet.infrastructure.quartz.ImportSchedulerProperties
import no.ruter.kosi.fleet.infrastructure.quartz.ReconstructVehicleJob
import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDataMap
import org.quartz.JobKey
import org.quartz.Scheduler
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.Locale
import java.util.UUID

@Component
class JobSchedulerManager(
    private val jobService: JobService,
    private val scheduler: Scheduler,
    private val importSchedulerProperties: ImportSchedulerProperties,
) {
    private val logger = LoggerFactory.getLogger(JobSchedulerManager::class.java)

    // TODO these are not that useful
    private val scheduledImportJobKey = JobKey.jobKey("scheduledCompleteImport", "imports")
    private val manualImportJobKey = JobKey.jobKey("manualCompleteImport", "imports")

    @PostConstruct
    fun initializeScheduler() {
        if (importSchedulerProperties.enabled) {
            schedulePeriodicImports(importSchedulerProperties.cron)
            logger.info("Scheduled fleet import with cron expression: {}", importSchedulerProperties.cron)
        } else {
            logger.info("Scheduled imports are disabled in configuration")
        }
    }

    private fun schedulePeriodicImports(cronExpression: String) {
        jobService.deschedulePendingOrScheluduledJobs()

        val newFleetJob =
            jobService.createJob(
                type = FleetJob.JobType.COMPLETE_IMPORT,
                manual = false,
                quartzJobId = scheduledImportJobKey.name,
                quartzJobGroup = scheduledImportJobKey.group,
            )

        MdcUtils.setJobId(newFleetJob.jobId)
        logger.info("Creating scheduled import job with ID {}", newFleetJob.jobId)

        val jobDetail =
            JobBuilder
                .newJob(CompleteImportJob::class.java)
                .withIdentity(scheduledImportJobKey)
                .usingJobData("jobId", newFleetJob.jobId)
                .usingJobData("jobType", "SCHEDULED")
                .build()

        val trigger =
            TriggerBuilder
                .newTrigger()
                .withIdentity("cronImportTrigger", "imports")
                .usingJobData("dummy", "dummy") // Work around Quartz deserialization bug // TODO check if still necessary
                .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression))
                .build()

        try {
            scheduler.scheduleJob(jobDetail, trigger)
            logger.info("Successfully scheduled periodic import job with ID {}", newFleetJob.jobId)
        } catch (e: Exception) {
            logger.error("Error scheduling periodic fleet import", e)
            jobService.failJob(newFleetJob.jobId, "Failed to schedule: ${e.message}")
        }
    }

    fun startImportFromDate(importDateStr: String): Boolean {
        val activeFleetJobs =
            jobService
                .findActiveJobs()
                .filter {
                    it.type == FleetJob.JobType.COMPLETE_IMPORT ||
                        it.type == FleetJob.JobType.IMPORT_FRIDA_VEHICLES ||
                        it.type == FleetJob.JobType.IMPORT_AUTOSYS
                }

        if (activeFleetJobs.isNotEmpty()) {
            logger.warn(
                "Attempt to start import while another import is in progress (ID {})",
                activeFleetJobs.first().jobId,
            )
            return false
        }

        logger.info("Starting import from date: {}", importDateStr)

        val fleetJob =
            jobService.createJob(
                type = FleetJob.JobType.COMPLETE_IMPORT,
                manual = true,
                quartzJobId = "Import-${UUID.randomUUID()}",
                quartzJobGroup = "imports",
            )

        MdcUtils.setJobId(fleetJob.jobId)
        logger.info("Starting import with ID {}", fleetJob.jobId)

        return try {
            val jobDetail =
                JobBuilder
                    .newJob(CompleteImportJob::class.java)
                    .withIdentity("ImportJob-${fleetJob.jobId}", "imports")
                    .usingJobData("jobId", fleetJob.jobId)
                    .apply {
                        importDateStr.let { dateStr ->
                            try {
                                val importDate = Instant.parse(dateStr)
                                usingJobData("importDate", importDate.toEpochMilli())
                            } catch (e: Exception) {
                                logger.warn("Invalid import date format '{}', using default", dateStr)
                            }
                        }
                    }
                    .build()

            val trigger =
                TriggerBuilder
                    .newTrigger()
                    .withIdentity("ImportTrigger-${fleetJob.jobId}", "imports")
                    .usingJobData("dummy", "dummy") // Work around Quartz deserialization bug
                    .startNow()
                    .build()

            scheduler.scheduleJob(jobDetail, trigger)
            logger.info("Import job scheduled successfully with ID {}", fleetJob.jobId)
            true
        } catch (e: Exception) {
            logger.error("Error scheduling import job", e)
            jobService.failJob(fleetJob.jobId, "Failed to schedule: ${e.message}")
            false
        }
    }

    fun scheduleJob(
        parentFleetJob: FleetJob? = null,
        fleetJobType: FleetJob.JobType,
        jobClass: Class<out org.quartz.Job>,
        importDate: Instant? = null,
        parameters: Map<String, Any> = emptyMap(),
        startNow: Boolean = true,
    ): FleetJob {
        val job =
            if (parentFleetJob != null) {
                jobService.createChildJob(
                    parentFleetJob = parentFleetJob,
                    type = fleetJobType,
                    quartzJobId = generateJobKey(fleetJobType),
                    quartzJobGroup = fleetJobType.toString(),
                )
            } else {
                jobService.createJob(
                    type = fleetJobType,
                    manual = true,
                    quartzJobId = generateJobKey(fleetJobType),
                    quartzJobGroup = fleetJobType.toString(),
                )
            }

        val jobDataMap =
            JobDataMap().apply {
                put("jobId", job.jobId) // make sure jobId is always present
                put("jobType", fleetJobType.toString())
                if (parentFleetJob != null) {
                    put("parentJobId", parentFleetJob.jobId)
                }
                // Add import date if provided
                importDate?.let { date ->
                    put("importDate", date.toEpochMilli())
                    logger.debug("Added importDate to job data for {}: {}", fleetJobType, date)
                }
                parameters.forEach { (key, value) -> put(key, value) }
            }

        val jobKey = JobKey.jobKey(job.quartzJobId, job.quartzJobGroup)
        val jobDetail =
            JobBuilder
                .newJob(jobClass)
                .withIdentity(jobKey)
                .usingJobData(jobDataMap)
                .build()

        val triggerId = "trigger-${job.quartzJobId}"
        val trigger =
            TriggerBuilder
                .newTrigger()
                .withIdentity(triggerId, job.quartzJobGroup)
                .usingJobData("dummy", "dummy") // work around Quartz deserialization bug
                .apply {
                    if (startNow) {
                        startNow()
                    }
                }.build()

        try {
            scheduler.scheduleJob(jobDetail, trigger)
            logger.info("Job scheduled successfully: {} with ID {}", fleetJobType, job.jobId)
            return job
        } catch (e: Exception) {
            logger.error("Error scheduling job: {}", e.message, e)
            jobService.failJob(job.jobId, "Failed to schedule: ${e.message}")
            throw e
        }
    }

    fun cancelJob(jobId: Int): Boolean {
        val job = jobService.findJob(jobId) ?: return false

        MdcUtils.setJobId(job.jobId)
        logger.info("Cancelling job with ID {}", job.jobId)
        jobService.cancelJob(job.jobId)

        // also try to cancel the Quartz job if it's still running
        try {
            if (job.quartzJobId != null && job.quartzJobGroup != null) {
                val jobKey = JobKey.jobKey(job.quartzJobId, job.quartzJobGroup)
                val context =
                    scheduler.currentlyExecutingJobs
                        .find { it.jobDetail.key == jobKey }

                if (context != null) {
                    context.result = "CANCELLED"
                    context.put("status", "CANCELLED")
                } else {
                    scheduler.deleteJob(jobKey)
                }
            }
        } catch (e: Exception) {
            logger.error("Error cancelling Quartz job for job {}", job.jobId, e)
        }
        return true
    }

    fun cancelActiveImport(): Boolean {
        val activeFleetJobs =
            jobService
                .findActiveJobs()
                .filter { it.type == FleetJob.JobType.COMPLETE_IMPORT || it.parentJobId == null }

        if (activeFleetJobs.isEmpty()) {
            logger.info("No active import to cancel")
            return false
        }

        var success = true
        activeFleetJobs.forEach { job ->
            success = success && cancelJob(job.jobId)
        }

        return success
    }

    fun isAnyImportInProgress(): Boolean {
        val activeFleetJobs =
            jobService
                .findActiveJobs()
                .filter {
                    it.type == FleetJob.JobType.COMPLETE_IMPORT ||
                        it.type == FleetJob.JobType.IMPORT_FRIDA_VEHICLES ||
                        it.type == FleetJob.JobType.IMPORT_FRIDA_CONTRACTS ||
                        it.type == FleetJob.JobType.IMPORT_AUTOSYS
                }

        return activeFleetJobs.isNotEmpty()
    }

    fun isAnyReconstructionInProgress(): Boolean {
        val activeFleetJobs =
            jobService
                .findActiveJobs()
                .filter { it.type == FleetJob.JobType.VEHICLE_RECONSTRUCTION }

        return activeFleetJobs.isNotEmpty()
    }

    fun getImportProgress(): Map<String, Any?> {
        val activeFleetJobs =
            jobService
                .findActiveJobs()
                .filter { it.type == FleetJob.JobType.COMPLETE_IMPORT }

        if (activeFleetJobs.isNotEmpty()) {
            MdcUtils.setJobId(activeFleetJobs.first().jobId)
            logger.debug("Getting progress for active import ID {}", activeFleetJobs.first().jobId)
            return jobService.getJobStatus(activeFleetJobs.first().jobId)
        }

        val recentFleetJob = jobService.findLatestJobByType(FleetJob.JobType.COMPLETE_IMPORT)
        if (recentFleetJob != null) {
            MdcUtils.setJobId(recentFleetJob.jobId)
            logger.debug("Getting status for recent import ID {}", recentFleetJob.jobId)
            val status = jobService.getJobStatus(recentFleetJob.jobId)
            return status + mapOf("inProgress" to false)
        }

        logger.debug("No import history found")
        return mapOf(
            "inProgress" to false,
            "message" to "No import in progress or history found.",
        )
    }

    private fun generateJobKey(fleetJobType: FleetJob.JobType): String =
        "${fleetJobType.toString().lowercase(Locale.getDefault())}-${UUID.randomUUID()}"
        
    fun startReconstructionFromDate(reconstructionDateStr: String): Boolean {
        val activeReconstructionJobs =
            jobService
                .findActiveJobs()
                .filter { it.type == FleetJob.JobType.VEHICLE_RECONSTRUCTION }

        if (activeReconstructionJobs.isNotEmpty()) {
            logger.warn(
                "Attempt to start reconstruction while another reconstruction is in progress (ID {})",
                activeReconstructionJobs.first().jobId,
            )
            return false
        }

        logger.info("Starting vehicle reconstruction from date: {}", reconstructionDateStr)

        val job = jobService.createJob(
            type = FleetJob.JobType.VEHICLE_RECONSTRUCTION,
            manual = true,
            quartzJobId = "reconstructionFromDate-${UUID.randomUUID()}",
            quartzJobGroup = "reconstruction",
        )

        return try {
            val jobDetail = JobBuilder.newJob(ReconstructVehicleJob::class.java)
                .withIdentity("reconstructionFromDateJob-${job.jobId}", "reconstruction")
                .usingJobData("jobId", job.jobId)
                .apply {
                    reconstructionDateStr.let { dateStr ->
                        try {
                            val reconstructionDate = Instant.parse(reconstructionDateStr);
                            usingJobData("reconstructionDate", reconstructionDate.toString())
                        } catch (e: Exception) {
                            logger.warn("Invalid reconstruction date format '{}', using default", dateStr)
                        }
                    }
                }
                .build()

            val trigger = TriggerBuilder.newTrigger()
                .withIdentity("reconstructionFromDateTrigger-${job.jobId}", "reconstruction")
                .startNow()
                .build()

            scheduler.scheduleJob(jobDetail, trigger)
            logger.info("Vehicle reconstruction from date job scheduled with ID: {}", job.jobId)
            true
        } catch (e: Exception) {
            logger.error("Error scheduling vehicle reconstruction from date job", e)
            jobService.failJob(job.jobId, "Failed to schedule: ${e.message}")
            false
        }
    }
}
