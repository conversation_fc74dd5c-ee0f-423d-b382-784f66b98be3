package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
class FoldingSeatsProcessor : BaseFieldProcessor() {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val fieldNames = listOf(
            "customAttr_Klappseter",
        )

        val eventData = getFirstNotNullValueEventData(aggregatedEventData, fieldNames)
        val rawValue = eventData?.fieldValue

        if (rawValue.isNullOrBlank()) {
            // Try fallback from snapshot
            val fallbackValue = lastSnapshot?.features?.comfort?.foldingSeats
            if (fallbackValue != null && fallbackValue >= 0) {
                return ProcessedField(
                    originProcessor = this.javaClass.kotlin,
                    value = fallbackValue,
                    quality = VehicleFieldQuality(
                        value = fallbackValue,
                        type = QualityType.STALE_VALUE,
                        message = "Using fallback folding seats count from snapshot"
                    )
                ).right()
            }

            return Either.Left(
                ProcessingError.MissingRequiredData(
                    processor = this.javaClass.kotlin,
                    message = "No folding seats data found"
                )
            )
        }

        val parsedValue = rawValue.toIntOrNull()
        if (parsedValue == null || parsedValue < 0) {
            return Either.Left(
                ProcessingError.InvalidFormat(
                    processor = this.javaClass.kotlin,
                    value = rawValue,
                    message = "Invalid folding seats count: '$rawValue'"
                )
            )
        }

        return ProcessedField(
            originProcessor = this.javaClass.kotlin,
            value = parsedValue,
            quality = VehicleFieldQuality(
                value = parsedValue,
                type = QualityType.VALID,
                message = ""
            )
        ).right()
    }
}
