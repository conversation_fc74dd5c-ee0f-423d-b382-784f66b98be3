package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.dto.APCInfoDto
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
class ApcProcessor : BaseFieldProcessor() {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val countFieldNames = listOf(
            "customAttr_APC_sensorer_antall",
        )
        
        val typeFieldNames = listOf(
            "customAttr_APC_sensorer_type",
        )

        val countEventData = getFirstNotNullValueEventData(aggregatedEventData, countFieldNames)
        val typeEventData = getFirstNotNullValueEventData(aggregatedEventData, typeFieldNames)

        val rawCount = countEventData?.fieldValue
        val rawType = typeEventData?.fieldValue

        val count = when {
            rawCount.isNullOrBlank() -> 0
            else -> {
                val parsedCount = rawCount.toIntOrNull()
                if (parsedCount != null && parsedCount >= 0) parsedCount else 0
            }
        }

        val type = when {
            rawType.isNullOrBlank() -> ""
            else -> rawType.trim()
        }

        val (qualityType, message) = when {
            countEventData == null && typeEventData == null -> {
                // Try fallback from snapshot
                val fallbackApc = lastSnapshot?.features?.apc
                if (fallbackApc != null && (fallbackApc.count > 0 || fallbackApc.type.isNotBlank())) {
                    val apcInfo = APCInfoDto(
                        count = fallbackApc.count,
                        type = fallbackApc.type
                    )
                    return ProcessedField(
                        originProcessor = this.javaClass.kotlin,
                        value = apcInfo,
                        quality = VehicleFieldQuality(
                            value = apcInfo,
                            type = QualityType.STALE_VALUE,
                            message = "Using fallback APC data from snapshot"
                        )
                    ).right()
                }

                Pair(QualityType.MISSING_VALUE, "No APC data found")
            }
            rawCount.isNullOrBlank() && rawType.isNullOrBlank() -> {
                Pair(QualityType.MISSING_VALUE, "No APC data found")
            }
            countEventData == null || typeEventData == null -> {
                Pair(QualityType.MISSING_VALUE, "Partial APC data found")
            }
            rawCount != null && rawCount != count.toString() -> {
                Pair(QualityType.ENRICHED_VALUE, "Parsed APC count from '$rawCount' to '$count'")
            }
            else -> {
                Pair(QualityType.VALID, "")
            }
        }

        val apcInfo = APCInfoDto(
            count = count,
            type = type
        )

        return ProcessedField(
            originProcessor = this.javaClass.kotlin,
            value = apcInfo,
            quality = VehicleFieldQuality(
                value = apcInfo,
                type = qualityType,
                message = message
            )
        ).right()
    }
}
