package no.ruter.kosi.fleet.domain.vehicle

import arrow.core.Either
import arrow.core.NonEmptyList
import arrow.core.raise.either
import arrow.core.raise.ensure
import arrow.core.raise.mapOrAccumulate
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQualityEntity
import no.ruter.kosi.fleet.domain.quality.VehicleQualityEntity
import no.ruter.kosi.fleet.domain.quality.dto.VehicleQualityDto
import no.ruter.kosi.fleet.domain.scd2.ISCD2Service
import no.ruter.kosi.fleet.domain.vehicle.VehicleAvroMapper.buildVehicleAvroRecord
import no.ruter.kosi.fleet.domain.vehicle.capacity.VehicleCapacityService.Companion.captureMdcContext
import no.ruter.kosi.fleet.domain.vehicle.dto.VehicleDto
import no.ruter.kosi.fleet.domain.vehicle.dto.VehicleDtoMapper
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleQualityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.time.LocalDate

@Service
@Primary
class VehicleService(
    private val reconstructionService: VehicleReconstructionService,
    @Qualifier("vehicleSCD2Service") private val vehicleSCD2Service: ISCD2Service<VehicleEntity, Int, String>,
    @Qualifier("vehicleQualitySCD2Service") private val vehicleQualitySCD2Service: ISCD2Service<VehicleQualityEntity, Int, String>,
    @Qualifier("auroraVehicleRepository") private val vehicleRepository: AuroraVehicleRepository,
    private val vehicleQualityRepository: AuroraVehicleQualityRepository,
    private val kafkaVehicleProducerService: KafkaVehicleProducerService,
    private val vehicleDtoMapper: VehicleDtoMapper,
    private val eventDataRepository: AuroraEventDataRepository,
) {
    private val logger = LoggerFactory.getLogger(VehicleService::class.java)

    /**
     * reconstructs vehicles from a specific date forward using eventdata
     * deletes existing vehicle records after the reconstruction date and rebuilds scd2 timeline
     */
    @Transactional
    fun reconstructAndSave(
        vehicle: InternalVehicle,
        timestamp: Instant
    ) {
        MdcUtils.setVehicleRef(vehicle.vehicleRef)

        logger.info("VehicleService.reconstructAndSave called with timestamp: {}", timestamp)
        logger.info("Reconstructing vehicle {} from date: {}", vehicle.vehicleRef, timestamp)

        reconstructFromDate(vehicle, timestamp)
    }

    /**
     * reconstructs vehicles from a specific date forward using eventdata
     * deletes existing vehicle records after the reconstruction date and rebuilds scd2 timeline
     */
    @Transactional
    fun reconstructFromDate(vehicle: InternalVehicle, reconstructionDate: Instant) {
        val businessKey = FleetVehicleRefGenerator.generateFleetVehicleRef(vehicle.sourceId).first

        logger.info("Reconstructing vehicle {} from date: {}", vehicle.vehicleRef, reconstructionDate)

        // 1. find eventdata that's valid from the reconstruction date
        val validEventData = eventDataRepository.findAllHistoricalByVehicleId(vehicle.vehicleId)
            .filter { eventData ->
                eventData.effectiveTo == null || eventData.effectiveTo!! > reconstructionDate
            }
            .filter { eventData ->
                eventData.effectiveFrom >= reconstructionDate
            }

        if (validEventData.isEmpty()) {
            logger.info("No valid EventData found for vehicle {} from date {}", vehicle.vehicleRef, reconstructionDate)
            return
        }

        logger.info("Found {} valid EventData records for vehicle {} from date {}",
            validEventData.size, vehicle.vehicleRef, reconstructionDate)

        // 2. delete existing vehicle records after the reconstruction date
        deleteVehicleRecordsAfterDate(businessKey, reconstructionDate)

        // 3. rebuild scd2 timeline from eventdata changes
        val timelineVersions = createTimelineFromEventData(validEventData)

        logger.info("Creating {} vehicle versions for {} from date {}",
            timelineVersions.size, vehicle.vehicleRef, reconstructionDate)

        // create vehicle versions for each time period
        val vehicleEntities = mutableListOf<VehicleEntity>()
        val qualityEntities = mutableListOf<VehicleQualityEntity>()

        timelineVersions.forEach { (effectiveFrom, eventDataAtTime) ->
            try {
                val result = reconstructionService.reconstructVehicleFromEventData(
                    vehicleId = vehicle.vehicleId,
                    eventDataList = eventDataAtTime,
                    lastSnapshot = null,
                    timestamp = effectiveFrom
                )

                result?.let { (vehicleDto, qualityDto) ->
                    val vehicleEntity = vehicleDtoMapper.toEntity(vehicleDto).apply {
                        this.effectiveFrom = Instant.EPOCH
                    }

                    val vehicleQualityEntity = VehicleQualityEntity(
                        vehicleId = vehicle.vehicleId,
                        businessKey = businessKey
                    ).apply {
                        this.effectiveFrom = Instant.EPOCH

                        qualityDto.fieldQualities.forEach { fq ->
                            this.fieldQualities.add(
                                VehicleFieldQualityEntity(
                                    vehicleQuality = this,
                                    fieldName = fq.fieldName,
                                    fieldValue = fq.fieldValue,
                                    qualityType = fq.qualityType,
                                    message = fq.message,
                                )
                            )
                        }
                    }

                    vehicleEntities.add(vehicleEntity)
                    qualityEntities.add(vehicleQualityEntity)

                    logger.debug("Prepared vehicle version for {} at {}", vehicle.vehicleRef, effectiveFrom)
                }
            } catch (e: Exception) {
                logger.error("Error preparing vehicle version for {} at {}: {}",
                    vehicle.vehicleRef, effectiveFrom, e.message, e)
            }
        }

        val sortedVehicleEntities = vehicleEntities.sortedBy { it.effectiveFrom }
        val sortedQualityEntities = qualityEntities.sortedBy { it.effectiveFrom }
        val vehicleTimestamps = timelineVersions.keys.sorted()

        if (sortedVehicleEntities.isNotEmpty()) {
            sortedVehicleEntities.forEachIndexed { index, vehicleEntity ->
                val historicTimestamp = vehicleTimestamps[index]
                logger.debug("Saving vehicle version for {} with timestamp: {}", vehicle.vehicleRef, historicTimestamp)
                vehicleSCD2Service.saveNewVersion(vehicleEntity, historicTimestamp)
            }
            logger.info("Saved {} vehicle versions for {}", sortedVehicleEntities.size, vehicle.vehicleRef)
        }

        if (sortedQualityEntities.isNotEmpty()) {
            sortedQualityEntities.forEachIndexed { index, qualityEntity ->
                val historicTimestamp = vehicleTimestamps[index]
                logger.debug("Saving quality version for {} with timestamp: {}", vehicle.vehicleRef, historicTimestamp)
                vehicleQualitySCD2Service.saveNewVersion(qualityEntity, historicTimestamp)
            }
            logger.info("Saved {} quality versions for {}", sortedQualityEntities.size, vehicle.vehicleRef)
        }

        logger.info("Completed reconstruction for vehicle {} from date {}", vehicle.vehicleRef, reconstructionDate)
    }

    /**
     * delete existing vehicle records that have effectiveFrom after the reconstruction date
     */
    private fun deleteVehicleRecordsAfterDate(businessKey: String, reconstructionDate: Instant) {
        try {
            val vehicleVersionsToDelete = vehicleSCD2Service.findAllVersionsByBusinessKey(businessKey)
                .filter { it.effectiveFrom >= reconstructionDate }

            val qualityVersionsToDelete = vehicleQualitySCD2Service.findAllVersionsByBusinessKey(businessKey)
                .filter { it.effectiveFrom >= reconstructionDate }

            if (vehicleVersionsToDelete.isNotEmpty()) {
                vehicleVersionsToDelete.forEach { vehicleRepository.delete(it) }
                logger.info("Deleted {} vehicle versions after date {} for {}",
                    vehicleVersionsToDelete.size, reconstructionDate, businessKey)
            }

            if (qualityVersionsToDelete.isNotEmpty()) {
                qualityVersionsToDelete.forEach { vehicleQualityRepository.delete(it) }
                logger.info("Deleted {} quality versions after date {} for {}",
                    qualityVersionsToDelete.size, reconstructionDate, businessKey)
            }
        } catch (e: Exception) {
            logger.warn("Error deleting vehicle records after date {} for {}: {}",
                reconstructionDate, businessKey, e.message)
        }
    }

    /**
     * creates a timeline of vehicle scd2 change states by grouping EventData by effective time periods
     */
    private fun createTimelineFromEventData(eventDataList: List<EventData>): Map<Instant, List<EventData>> {
        val effectiveDates = eventDataList.map { it.effectiveFrom }.distinct().sorted()

        logger.debug("Found {} unique effective dates from EventData: {}",
            effectiveDates.size,
            effectiveDates.take(5).map { it.toString() })

        return effectiveDates.associateWith { effectiveDate ->
            eventDataList
                .filter { it.effectiveFrom <= effectiveDate }
                .groupBy { it.fieldName }
                .mapValues { (_, fieldEvents) ->
                    fieldEvents.maxByOrNull { it.effectiveFrom }!!
                }
                .values
                .toList()
        }
    }

    // cut and split a vehicle's events up to save all of the historal vehicles for this internalvehicle
    @Transactional
    fun reconstructAndSaveHistoricalTimeline(
        vehicle: InternalVehicle
    ) {
        MdcUtils.setVehicleRef(vehicle.vehicleRef)
        logger.info("Reconstructing full SCD2 history for vehicle ${vehicle.vehicleRef}")

        val allEventData = eventDataRepository.findAllHistoricalByVehicleId(vehicle.vehicleId)

        if (allEventData.isEmpty()) {
            logger.info("No historical event data found for vehicle ${vehicle.vehicleRef}")
            return
        }

        val timelineVersions = createHistoricalTimeline(allEventData)

        logger.info("Creating {} historical versions for vehicle {}", timelineVersions.size, vehicle.vehicleRef)

        val vehicleEntities = mutableListOf<VehicleEntity>()
        val qualityEntities = mutableListOf<VehicleQualityEntity>()

        timelineVersions.forEach { (effectiveFrom, eventDataAtTime) ->
            try {
                val result = reconstructionService.reconstructVehicleFromEventData(
                    vehicleId = vehicle.vehicleId,
                    eventDataList = eventDataAtTime,
                    lastSnapshot = null,
                    timestamp = effectiveFrom
                )

                result?.let { (vehicleDto, qualityDto) ->
                    val vehicleEntity = vehicleDtoMapper.toEntity(vehicleDto).apply {
                        // initialize this to epoch
                        this.effectiveFrom = Instant.EPOCH
                        businessKey = vehicle.vehicleRef
                    }

                    val vehicleQualityEntity = VehicleQualityEntity(
                        vehicleId = vehicle.vehicleId,
                        businessKey = vehicle.vehicleRef!!
                    ).apply {
                        // initialize this to epoch
                        this.effectiveFrom = Instant.EPOCH

                        // add field qualities from DTO
                        qualityDto.fieldQualities.forEach { fq ->
                            this.fieldQualities.add(
                                VehicleFieldQualityEntity(
                                    vehicleQuality = this,
                                    fieldName = fq.fieldName,
                                    fieldValue = fq.fieldValue,
                                    qualityType = fq.qualityType,
                                    message = fq.message,
                                )
                            )
                        }
                    }

                    vehicleEntities.add(vehicleEntity)
                    qualityEntities.add(vehicleQualityEntity)

                    logger.info("Prepared vehicle version for {} with historic timestamp: {}", vehicle.vehicleRef, effectiveFrom)
                }
            } catch (e: Exception) {
                logger.error("Error preparing vehicle version for {} at {}: {}",
                    vehicle.vehicleRef, effectiveFrom, e.message, e)
            }
        }

        // save in chronological order to build proper SCD2 timeline
        val sortedVehicleEntities = vehicleEntities.sortedBy { it.effectiveFrom }
        val sortedQualityEntities = qualityEntities.sortedBy { it.effectiveFrom }

        if (sortedVehicleEntities.isNotEmpty()) {
            val vehicleTimestamps = timelineVersions.keys.sorted()
            sortedVehicleEntities.forEachIndexed { index, vehicleEntity ->
                val historicTimestamp = vehicleTimestamps[index]
                logger.info("Saving vehicle version for {} with historic timestamp: {}", vehicle.vehicleRef, historicTimestamp)
                vehicleSCD2Service.saveNewVersion(vehicleEntity, historicTimestamp)
            }
            logger.info("Saved {} vehicle versions for {}", sortedVehicleEntities.size, vehicle.vehicleRef)
        }

        if (sortedQualityEntities.isNotEmpty()) {
            val qualityTimestamps = timelineVersions.keys.sorted()
            sortedQualityEntities.forEachIndexed { index, qualityEntity ->
                val historicTimestamp = qualityTimestamps[index]
                logger.info("Saving quality version for {} with historic timestamp: {}", vehicle.vehicleRef, historicTimestamp)
                vehicleQualitySCD2Service.saveNewVersion(qualityEntity, historicTimestamp)
            }
            logger.info("Saved {} quality versions for {}", sortedQualityEntities.size, vehicle.vehicleRef)
        }

        logger.info("Completed historical reconstruction for vehicle ${vehicle.vehicleRef}")
    }

    private fun createHistoricalTimeline(allEventData: List<EventData>): Map<Instant, List<EventData>> {
        // get all unique effective dates from the event data
        val effectiveDates = allEventData.map { it.effectiveFrom }.distinct().sorted()

        logger.info("Found {} unique effective dates from EventData: {}",
            effectiveDates.size,
            effectiveDates.take(5).map { it.toString() })

        return effectiveDates.associateWith { effectiveDate ->
            // for each effective date, get the state of all fields at that time
            allEventData
                .filter { it.effectiveFrom <= effectiveDate }
                .groupBy { it.fieldName }
                .mapValues { (_, fieldEvents) ->
                    // get the latest value for each field up to this date
                    fieldEvents.maxByOrNull { it.effectiveFrom }!!
                }
                .values
                .toList()
        }
    }

    fun publishVehiclesToKafka(onVehicleProcessed: ((count: Int) -> Unit)? = null): Either<NonEmptyList<VehiclePublishError>, Unit> =
        either {
            val vehicles = vehicleRepository.findAllCurrent()

            mapOrAccumulate(vehicles.withIndex()) { (idx, entity) ->
                MdcUtils.setVehicleRef(entity.identification.chassisNumber)

                val record = buildVehicleAvroRecord(entity).mapLeft {
                    VehiclePublishError.RecordBuildError(
                        vehicleId = entity.internalVehicleId,
                        vehicleRef = entity.identification.chassisNumber,
                        cause = it.cause ?: RuntimeException("Unknown error during Avro record build"),
                        mdc = captureMdcContext()
                    )
                }.bind()

                ensure(!entity.identification.chassisNumber.isNullOrBlank()) {
                    VehiclePublishError.MissingChassisNumberError(
                        vehicleId = entity.internalVehicleId,
                        message = "Missing chassis number",
                        mdc = captureMdcContext()
                    )
                }
                val chassisNumber = entity.identification.chassisNumber!!

                Either.catch {
                    kafkaVehicleProducerService.sendMessage(chassisNumber, record)
                }.mapLeft { ex ->
                    VehiclePublishError.KafkaPublishError(
                        key = chassisNumber,
                        cause = ex,
                        mdc = captureMdcContext()
                    )
                }.bind()

                onVehicleProcessed?.invoke(idx + 1)
            }

            Unit
        }

    fun getLatestVehicle(vehicleRef: String): VehicleDto? {
        MdcUtils.setVehicleRef(vehicleRef)
        val latestVehicle = vehicleRepository.findCurrentByVehicleRef(vehicleRef)
        return if (latestVehicle != null) {
            vehicleDtoMapper.toDto(latestVehicle)
        } else {
            null
        }
    }

    fun getAllVehicles(): List<VehicleDto> =
        vehicleRepository.findAllCurrent()
            .map { vehicleDtoMapper.toDto(it) }

    fun getVehiclesByContract(contractId: Int): List<VehicleDto> =
        vehicleRepository.findAll()
            .filter { it.contractor?.id == contractId }
            .map { vehicleDtoMapper.toDto(it) }

    fun getReconstructedVehicleAtTime(
        vehicleRef: String,
        timestamp: Instant? = Instant.now()
    ): Either<VehicleError, Pair<VehicleDto, VehicleQualityDto>> = try {
        reconstructionService.reconstructVehicle(vehicleRef, timestamp)
?.let { Either.Right(it) }
            ?: Either.Left(VehicleError.NotFound)
    } catch (e: NoSuchElementException) {
        Either.Left(VehicleError.NotFound)
    } catch (e: Exception) {
        Either.Left(VehicleError.Unexpected("Failed to reconstruct vehicle by ref '$vehicleRef': ${e.message}", e))
    }

    @Transactional(readOnly = true)
    fun getAllSnapshotsWithRelationships(): List<VehicleEntity> =
        vehicleRepository.findAllWithRelationships()

    @Transactional(readOnly = true)
    fun getAllVersionsWithRelationships(): List<VehicleEntity> =
        vehicleRepository.findAllVersionsWithRelationships()

    sealed class VehicleError {
        object NotFound : VehicleError()
        data class Unexpected(val message: String, val cause: Throwable? = null) : VehicleError()
    }

    sealed class VehiclePublishError {
        abstract val cause: Throwable?
        abstract val mdc: Map<String, String>

        data class FetchError(
            val message: String,
            override val cause: Throwable,
            override val mdc: Map<String, String> = emptyMap()
        ) : VehiclePublishError()

        data class RecordBuildError(
            val vehicleId: Int,
            val vehicleRef: String?,
            override val cause: Throwable,
            override val mdc: Map<String, String>
        ) : VehiclePublishError()

        data class MissingChassisNumberError(
            val vehicleId: Int,
            val message: String,
            override val cause: Throwable? = null,
            override val mdc: Map<String, String>
        ) : VehiclePublishError()

        data class KafkaPublishError(
            val key: String,
            override val cause: Throwable,
            override val mdc: Map<String, String>
        ) : VehiclePublishError()

        data class UnexpectedError(
            val message: String,
            override val cause: Throwable?,
            override val mdc: Map<String, String>
        ) : VehiclePublishError()
    }
}
