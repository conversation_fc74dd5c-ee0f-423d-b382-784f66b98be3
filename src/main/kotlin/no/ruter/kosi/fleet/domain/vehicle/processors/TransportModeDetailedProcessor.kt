package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
class TransportModeDetailedProcessor : BaseFieldProcessor() {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val fieldNames = listOf(
            "customAttr_Buss_type",
        )

        val eventData = getFirstNotNullValueEventData(aggregatedEventData, fieldNames)
        val rawValue = eventData?.fieldValue

        if (rawValue.isNullOrBlank()) {
            return Either.Left(
                ProcessingError.MissingRequiredData(
                    processor = this.javaClass.kotlin,
                    message = "No detailed transport mode data found"
                )
            )
        }

        val trimmedValue = rawValue.trim()
        
        // Validate that the value is not empty after trimming
        if (trimmedValue.isEmpty()) {
            return Either.Left(
                ProcessingError.InvalidFormat(
                    processor = this.javaClass.kotlin,
                    value = rawValue,
                    message = "Detailed transport mode is empty or contains only whitespace"
                )
            )
        }

        return ProcessedField(
            originProcessor = this.javaClass.kotlin,
            value = trimmedValue,
            quality = VehicleFieldQuality(
                value = trimmedValue,
                type = QualityType.VALID,
                message = ""
            )
        ).right()
    }
}
