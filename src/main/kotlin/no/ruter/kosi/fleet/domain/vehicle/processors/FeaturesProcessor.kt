package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.dto.AccessibilityDto
import no.ruter.kosi.fleet.domain.vehicle.dto.APCInfoDto
import no.ruter.kosi.fleet.domain.vehicle.dto.ComfortDto
import no.ruter.kosi.fleet.domain.vehicle.dto.FeaturesDto
import no.ruter.kosi.fleet.domain.vehicle.dto.OtherFeaturesDto
import no.ruter.kosi.fleet.domain.vehicle.dto.SafetyDto
import no.ruter.kosi.fleet.domain.vehicle.dto.SignageDto
import no.ruter.kosi.fleet.domain.vehicle.dto.TicketingDto
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.DependsOn
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@DependsOn(ApcProcessor::class, FoldingSeatsProcessor::class)
@Component
class FeaturesProcessor : BaseFieldProcessor("features") {
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?
    ): Either<ProcessingError, ProcessedField> {
        val foldingSeats = processorResults[FoldingSeatsProcessor::class]?.value as? Int

        val comfort = ComfortDto(
            airConditioning = false,
            heater = null,
            readingLights = null,
            armrest = null,
            backrest = null,
            foldingSeats = foldingSeats
        )

        val accessibility = AccessibilityDto(
            lowFloor = false,
            lowEntry = "",
            lift = null,
            ramp = false,
            kneeling = null,
            accessibilityRamp = null,
            wheelchairPlaces = null,
            walkerPlaces = null,
            covid19CapacityLimit = null
        )

        val apcInfo = processorResults[ApcProcessor::class]?.value as? APCInfoDto ?: APCInfoDto(
            count = 0,
            type = ""
        )

        val safety = SafetyDto(
            seatBelts = "",
            seatBeltReminder = false,
            visualBeltReminder = null,
            airbagPassenger = null,
            alcoLock = "",
            fireExtinguisher = false,
            fireExtinguisherInEngineCompartment = false,
            defibrillators = null,
            cameraSurveillanceSafety = false,
            cameraSurveillanceTrafficSafety = false,
        )

        val signage = SignageDto(
            destinationSign = "",
            internalInformationSign = false,
            externalDestinationCall = null,
            stopAnnouncements = false
        )

        val ticketing = TicketingDto(
            cardReaders = 0,
            machineNumber = "",
            system = ""
        )

        val other = OtherFeaturesDto(
            bikeRack = null,
            boosterSeat = null,
            childSeats = null,
            strollerPlaces = 0,
            passengerCounter = false,
            ticketing = ticketing,
            vehicleComputer = ""
        )

        val features = FeaturesDto(
            comfort = comfort,
            accessibility = accessibility,
            safety = safety,
            signage = signage,
            apc = apcInfo,
            other = other
        )

        return ProcessedField(
            originProcessor = this.javaClass.kotlin,
            value = features,
            quality = VehicleFieldQuality(
                value = features,
                type = QualityType.VALID
            )
        ).right()
    }
}