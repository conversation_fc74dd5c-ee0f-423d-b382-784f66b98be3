package no.ruter.kosi.fleet.web

import no.ruter.kosi.fleet.domain.job.JobSchedulerManager
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@RestController
@RequestMapping("/reconstructions")
class ReconstructionController(
    private val jobSchedulerManager: JobSchedulerManager,
) {
    private val logger = LoggerFactory.getLogger(ReconstructionController::class.java)

    @PostMapping("/start")
    fun startReconstructionFromDate(
        @RequestParam(required = false) fromInstant: String = Instant.now().toString()
    ): ResponseEntity<Map<String, Any>> {
        return try {
            val started = jobSchedulerManager.startReconstructionFromDate(fromInstant)
            if (started) {
                ResponseEntity.accepted().body(
                    mapOf(
                        "message" to "Vehicle reconstruction from date started successfully",
                        "reconstructionDate" to fromInstant.toString(),
                        "status" to "started"
                    )
                )
            } else {
                ResponseEntity.status(500).body(
                    mapOf(
                        "error" to "Failed to start reconstruction from date",
                        "status" to "failed"
                    )
                )
            }
        } catch (e: Exception) {
            logger.error("Error starting reconstruction from date: {}", e.message, e)
            ResponseEntity.status(500).body(
                mapOf(
                    "error" to "Internal server error: ${e.message}",
                    "status" to "error"
                )
            )
        }
    }
}
