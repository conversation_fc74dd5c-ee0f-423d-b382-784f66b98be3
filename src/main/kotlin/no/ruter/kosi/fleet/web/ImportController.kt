package no.ruter.kosi.fleet.web

import no.ruter.kosi.fleet.domain.job.JobSchedulerManager
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@RestController
@RequestMapping("/imports")
class ImportController(
    private val jobSchedulerManager: JobSchedulerManager,
) {
    private val logger = LoggerFactory.getLogger(ImportController::class.java)

    @GetMapping("/progress")
    fun getImportProgress(): ResponseEntity<Map<String, Any?>> =
        try {
            val progress = jobSchedulerManager.getImportProgress()
            ResponseEntity.ok(progress)
        } catch (e: Exception) {
            logger.error("Error fetching import progress: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "error" to "Failed to fetch import progress: ${e.message}",
                ),
            )
        }

    @PostMapping("/cancel")
    fun cancelImport(): ResponseEntity<Map<String, Any>> =
        try {
            val cancelled = jobSchedulerManager.cancelActiveImport()
            if (cancelled) {
                ResponseEntity.ok(
                    mapOf(
                        "success" to true,
                        "message" to "Active import cancelled successfully.",
                    ),
                )
            } else {
                ResponseEntity.badRequest().body(
                    mapOf(
                        "success" to false,
                        "message" to "No active import to cancel.",
                    ),
                )
            }
        } catch (e: Exception) {
            logger.error("Error cancelling import: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "Error cancelling import: ${e.message}",
                ),
            )
        }

    @PostMapping("/start")
    fun startImportFromDate(
        @RequestParam(required = false) fromInstant: String = Instant.now().toString()
    ): ResponseEntity<Map<String, Any>> {
        return try {
            if (jobSchedulerManager.isAnyImportInProgress()) {
                ResponseEntity.badRequest().body(
                    mapOf(
                        "error" to "Cannot start import while another import is in progress",
                        "status" to "rejected"
                    )
                )
            } else {
                val started = jobSchedulerManager.startImportFromDate(fromInstant)
                if (started) {
                    ResponseEntity.accepted().body(
                        mapOf(
                            "message" to "Import from date started successfully",
                            "importDate" to fromInstant,
                            "status" to "started"
                        )
                    )
                } else {
                    ResponseEntity.status(500).body(
                        mapOf(
                            "error" to "Failed to start import from date",
                            "status" to "failed"
                        )
                    )
                }
            }
        } catch (e: Exception) {
            logger.error("Error starting import from date: {}", e.message, e)
            ResponseEntity.status(500).body(
                mapOf(
                    "error" to "Internal server error: ${e.message}",
                    "status" to "error"
                )
            )
        }
    }
}
