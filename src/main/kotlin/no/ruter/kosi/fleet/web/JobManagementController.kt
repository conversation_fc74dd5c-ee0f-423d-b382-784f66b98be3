package no.ruter.kosi.fleet.web

import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobSchedulerManager
import no.ruter.kosi.fleet.domain.job.JobService
import org.slf4j.LoggerFactory
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant
import java.time.ZonedDateTime

@RestController
@RequestMapping("/jobs")
class JobManagementController(
    private val jobService: JobService,
    private val jobSchedulerManager: JobSchedulerManager,
) {
    private val logger = LoggerFactory.getLogger(JobManagementController::class.java)

    @GetMapping("")
    fun listJobs(
        @RequestParam(required = false) type: FleetJob.JobType?,
        @RequestParam(required = false) status: FleetJob.JobStatus?,
        @RequestParam(required = false) manual: Boolean?,
        @RequestParam(required = false) parentJobId: Int?,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) createdAfter: ZonedDateTime?,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) createdBefore: ZonedDateTime?,
        @RequestParam(required = false, defaultValue = "20") limit: Int,
        @RequestParam(required = false, defaultValue = "0") offset: Int,
    ): ResponseEntity<Map<String, Any>> =
        try {
            val allJobs = jobService.findAll()

            var filteredJobs = allJobs

            if (type != null) {
                filteredJobs = filteredJobs.filter { it.type == type }
            }

            if (status != null) {
                filteredJobs = filteredJobs.filter { it.status == status }
            }

            if (manual != null) {
                filteredJobs = filteredJobs.filter { it.manual == manual }
            }

            if (parentJobId != null) {
                filteredJobs = filteredJobs.filter { it.parentJobId == parentJobId }
            }

            if (createdAfter != null) {
                val createdAfterInstant = createdAfter.toInstant()
                filteredJobs = filteredJobs.filter { it.createdAt.isAfter(createdAfterInstant) }
            }

            if (createdBefore != null) {
                val createdBeforeInstant = createdBefore.toInstant()
                filteredJobs = filteredJobs.filter { it.createdAt.isBefore(createdBeforeInstant) }
            }

            val sortedJobs = filteredJobs.sortedByDescending { it.createdAt }

            val paginatedJobs = sortedJobs.drop(offset).take(limit)

            ResponseEntity.ok(
                mapOf(
                    "jobs" to paginatedJobs,
                    "total" to filteredJobs.size,
                    "limit" to limit,
                    "offset" to offset,
                ),
            )
        } catch (e: Exception) {
            logger.error("Error listing jobs: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "error" to "Failed to list jobs: ${e.message}",
                ),
            )
        }

    @GetMapping("/{jobId}")
    fun getJob(
        @PathVariable jobId: Int,
    ): ResponseEntity<Any> {
        return try {
            val job = jobService.findJob(jobId) ?: return ResponseEntity.notFound().build()

            ResponseEntity.ok(job)
        } catch (e: Exception) {
            logger.error("Error getting job $jobId: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "error" to "Failed to get job: ${e.message}",
                ),
            )
        }
    }

    @GetMapping("/{jobId}/status")
    fun getJobStatus(
        @PathVariable jobId: Int,
    ): ResponseEntity<Any> =
        try {
            val status = jobService.getJobStatus(jobId)
            ResponseEntity.ok(status)
        } catch (e: Exception) {
            logger.error("Error getting job status for job $jobId: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "error" to "Failed to get job status: ${e.message}",
                ),
            )
        }

    @PostMapping("/{jobId}/cancel")
    fun cancelJob(
        @PathVariable jobId: Int,
    ): ResponseEntity<Map<String, Any>> =
        try {
            val cancelled = jobSchedulerManager.cancelJob(jobId)
            if (cancelled) {
                ResponseEntity.ok(
                    mapOf(
                        "success" to true,
                        "message" to "Job cancelled successfully.",
                    ),
                )
            } else {
                ResponseEntity.badRequest().body(
                    mapOf(
                        "success" to false,
                        "message" to "No job found with ID $jobId.",
                    ),
                )
            }
        } catch (e: Exception) {
            logger.error("Error cancelling job $jobId: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "Error cancelling job: ${e.message}",
                ),
            )
        }
}
