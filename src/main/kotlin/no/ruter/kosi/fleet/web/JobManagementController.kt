package no.ruter.kosi.fleet.web

import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobSchedulerManager
import no.ruter.kosi.fleet.domain.job.JobService
import org.slf4j.LoggerFactory
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant
import java.time.ZonedDateTime

@RestController
@RequestMapping("/jobs")
class JobManagementController(
    private val jobService: JobService,
    private val jobSchedulerManager: JobSchedulerManager,
) {
    private val logger = LoggerFactory.getLogger(JobManagementController::class.java)

    @GetMapping("")
    fun listJobs(
        @RequestParam(required = false) type: FleetJob.JobType?,
        @RequestParam(required = false) status: FleetJob.JobStatus?,
        @RequestParam(required = false) manual: Boolean?,
        @RequestParam(required = false) parentJobId: Int?,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) createdAfter: ZonedDateTime?,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) createdBefore: ZonedDateTime?,
        @RequestParam(required = false, defaultValue = "20") limit: Int,
        @RequestParam(required = false, defaultValue = "0") offset: Int,
    ): ResponseEntity<Map<String, Any>> =
        try {
            val allJobs = jobService.findAll()

            var filteredJobs = allJobs

            if (type != null) {
                filteredJobs = filteredJobs.filter { it.type == type }
            }

            if (status != null) {
                filteredJobs = filteredJobs.filter { it.status == status }
            }

            if (manual != null) {
                filteredJobs = filteredJobs.filter { it.manual == manual }
            }

            if (parentJobId != null) {
                filteredJobs = filteredJobs.filter { it.parentJobId == parentJobId }
            }

            if (createdAfter != null) {
                val createdAfterInstant = createdAfter.toInstant()
                filteredJobs = filteredJobs.filter { it.createdAt.isAfter(createdAfterInstant) }
            }

            if (createdBefore != null) {
                val createdBeforeInstant = createdBefore.toInstant()
                filteredJobs = filteredJobs.filter { it.createdAt.isBefore(createdBeforeInstant) }
            }

            val sortedJobs = filteredJobs.sortedByDescending { it.createdAt }

            val paginatedJobs = sortedJobs.drop(offset).take(limit)

            ResponseEntity.ok(
                mapOf(
                    "jobs" to paginatedJobs,
                    "total" to filteredJobs.size,
                    "limit" to limit,
                    "offset" to offset,
                ),
            )
        } catch (e: Exception) {
            logger.error("Error listing jobs: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "error" to "Failed to list jobs: ${e.message}",
                ),
            )
        }

    @GetMapping("/{jobId}")
    fun getJob(
        @PathVariable jobId: Int,
    ): ResponseEntity<Any> {
        return try {
            val job = jobService.findJob(jobId) ?: return ResponseEntity.notFound().build()

            ResponseEntity.ok(job)
        } catch (e: Exception) {
            logger.error("Error getting job $jobId: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "error" to "Failed to get job: ${e.message}",
                ),
            )
        }
    }

    @GetMapping("/{jobId}/status")
    fun getJobStatus(
        @PathVariable jobId: Int,
    ): ResponseEntity<Any> =
        try {
            val status = jobService.getJobStatus(jobId)
            ResponseEntity.ok(status)
        } catch (e: Exception) {
            logger.error("Error getting job status for job $jobId: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "error" to "Failed to get job status: ${e.message}",
                ),
            )
        }

    @GetMapping("/imports/progress")
    fun getImportProgress(): ResponseEntity<Map<String, Any?>> =
        try {
            val progress = jobSchedulerManager.getImportProgress()
            ResponseEntity.ok(progress)
        } catch (e: Exception) {
            logger.error("Error fetching import progress: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "error" to "Failed to fetch import progress: ${e.message}",
                ),
            )
        }

    @PostMapping("/{jobId}/cancel")
    fun cancelJob(
        @PathVariable jobId: Int,
    ): ResponseEntity<Map<String, Any>> =
        try {
            val cancelled = jobSchedulerManager.cancelJob(jobId)
            if (cancelled) {
                ResponseEntity.ok(
                    mapOf(
                        "success" to true,
                        "message" to "Job cancelled successfully.",
                    ),
                )
            } else {
                ResponseEntity.badRequest().body(
                    mapOf(
                        "success" to false,
                        "message" to "No job found with ID $jobId.",
                    ),
                )
            }
        } catch (e: Exception) {
            logger.error("Error cancelling job $jobId: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "Error cancelling job: ${e.message}",
                ),
            )
        }

    @PostMapping("/imports/cancel")
    fun cancelImport(): ResponseEntity<Map<String, Any>> =
        try {
            val cancelled = jobSchedulerManager.cancelActiveImport()
            if (cancelled) {
                ResponseEntity.ok(
                    mapOf(
                        "success" to true,
                        "message" to "Active import cancelled successfully.",
                    ),
                )
            } else {
                ResponseEntity.badRequest().body(
                    mapOf(
                        "success" to false,
                        "message" to "No active import to cancel.",
                    ),
                )
            }
        } catch (e: Exception) {
            logger.error("Error cancelling import: ${e.message}", e)
            ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "Error cancelling import: ${e.message}",
                ),
            )
        }

    @PostMapping("/reconstruction/start")
    fun startReconstructionFromDate(
        @RequestParam(required = false) fromInstant: String = Instant.now().toString()
    ): ResponseEntity<Map<String, Any>> {
        return try {
            if (jobSchedulerManager.isAnyImportInProgress()) {
                ResponseEntity.badRequest().body(
                    mapOf(
                        "error" to "Cannot start reconstruction while another import is in progress",
                        "status" to "rejected"
                    )
                )
            } else {
                val started = jobSchedulerManager.startReconstructionFromDate(fromInstant)
                if (started) {
                    ResponseEntity.accepted().body(
                        mapOf(
                            "message" to "Vehicle reconstruction from date started successfully",
                            "reconstructionDate" to fromInstant.toString(),
                            "status" to "started"
                        )
                    )
                } else {
                    ResponseEntity.status(500).body(
                        mapOf(
                            "error" to "Failed to start reconstruction from date",
                            "status" to "failed"
                        )
                    )
                }
            }
        } catch (e: Exception) {
            logger.error("Error starting reconstruction from date: {}", e.message, e)
            ResponseEntity.status(500).body(
                mapOf(
                    "error" to "Internal server error: ${e.message}",
                    "status" to "error"
                )
            )
        }
    }

    @PostMapping("/import/start")
    fun startImportFromDate(
        @RequestParam(required = false) fromInstant: String = Instant.now().toString()
    ): ResponseEntity<Map<String, Any>> {
        return try {
            if (jobSchedulerManager.isAnyImportInProgress()) {
                ResponseEntity.badRequest().body(
                    mapOf(
                        "error" to "Cannot start import while another import is in progress",
                        "status" to "rejected"
                    )
                )
            } else {
                val started = jobSchedulerManager.startImportFromDate(fromInstant)
                if (started) {
                    ResponseEntity.accepted().body(
                        mapOf(
                            "message" to "Import from date started successfully",
                            "importDate" to fromInstant,
                            "status" to "started"
                        )
                    )
                } else {
                    ResponseEntity.status(500).body(
                        mapOf(
                            "error" to "Failed to start import from date",
                            "status" to "failed"
                        )
                    )
                }
            }
        } catch (e: Exception) {
            logger.error("Error starting import from date: {}", e.message, e)
            ResponseEntity.status(500).body(
                mapOf(
                    "error" to "Internal server error: ${e.message}",
                    "status" to "error"
                )
            )
        }
    }
}
